<!DOCTYPE html>
<html lang="zh-CN" data-theme="auto" data-font-size="md">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息编辑功能测试</title>
    
    <!-- 导入样式文件 -->
    <link rel="stylesheet" href="./styles/variables.css">
    <link rel="stylesheet" href="./styles/components.css">
    <link rel="stylesheet" href="./styles/layout.css">
    <link rel="stylesheet" href="./styles/themes.css">
    
    <style>
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .test-section h2 {
            margin-top: 0;
            margin-bottom: var(--spacing-md);
            color: var(--color-foreground);
        }
        
        .control-panel {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            flex-wrap: wrap;
        }
        
        .demo-messages {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            max-height: 400px;
            overflow-y: auto;
        }
        
        .instructions {
            background: var(--color-surface-hover);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .instructions h3 {
            margin-top: 0;
            margin-bottom: var(--spacing-sm);
            color: var(--color-primary);
        }
        
        .instructions ul {
            margin: 0;
            padding-left: var(--spacing-xl);
        }
        
        .instructions li {
            margin-bottom: var(--spacing-xs);
        }
        
        .feature-demo {
            border: 2px dashed var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin: var(--spacing-md) 0;
            text-align: center;
            color: var(--color-foreground-muted);
        }
        
        .log-output {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            font-family: monospace;
            font-size: var(--font-size-sm);
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>消息编辑功能测试</h1>
        
        <!-- 使用说明 -->
        <div class="instructions">
            <h3>📝 编辑功能使用说明</h3>
            <ul>
                <li><strong>点击编辑按钮</strong>：鼠标悬停在用户消息上，点击 ✏️ 按钮进入编辑模式</li>
                <li><strong>内联编辑</strong>：消息会变成可编辑的文本框，支持多行文本</li>
                <li><strong>保存编辑</strong>：点击"保存"按钮或按 Ctrl+Enter 保存更改</li>
                <li><strong>取消编辑</strong>：点击"取消"按钮或按 Esc 键取消编辑</li>
                <li><strong>自动调整</strong>：编辑器会根据内容自动调整高度</li>
                <li><strong>编辑标记</strong>：已编辑的消息会显示编辑时间</li>
            </ul>
        </div>
        
        <!-- 控制面板 -->
        <div class="test-section">
            <h2>测试控制</h2>
            <div class="control-panel">
                <button class="btn btn-primary" onclick="addSampleMessages()">添加示例消息</button>
                <button class="btn btn-secondary" onclick="addUserMessage()">添加用户消息</button>
                <button class="btn btn-secondary" onclick="addLongMessage()">添加长消息</button>
                <button class="btn btn-secondary" onclick="clearMessages()">清空消息</button>
                <button class="btn btn-ghost" onclick="clearLog()">清空日志</button>
            </div>
        </div>
        
        <!-- 功能演示 -->
        <div class="test-section">
            <h2>编辑功能演示</h2>
            <div class="demo-messages" id="messages">
                <div class="feature-demo">
                    点击上方"添加示例消息"按钮开始测试编辑功能
                </div>
            </div>
        </div>
        
        <!-- 编辑历史 -->
        <div class="test-section">
            <h2>编辑历史记录</h2>
            <div id="editHistory" class="log-output">
                编辑操作将在这里显示...
            </div>
        </div>
        
        <!-- 操作日志 -->
        <div class="test-section">
            <h2>操作日志</h2>
            <div id="logOutput" class="log-output">
                操作日志将在这里显示...
            </div>
        </div>
        
        <!-- Toast容器 -->
        <div class="toast-container" id="toastContainer"></div>
    </div>
    
    <script type="module">
        import { Store } from './scripts/state/store.js';
        import { MessageComponent } from './scripts/components/message.js';
        import { ToastComponent } from './scripts/components/toast.js';
        import { EventManager } from './scripts/utils/events.js';
        import { MessageTypes, MessageStatus } from './scripts/components/message-manager.js';
        
        // 初始化组件
        const store = new Store();
        const eventManager = new EventManager();
        const messageComponent = new MessageComponent(store, eventManager);
        const toastComponent = new ToastComponent();
        
        // 全局变量
        window.store = store;
        window.messageComponent = messageComponent;
        window.toastComponent = toastComponent;
        window.editHistory = [];
        
        // 初始化组件
        Promise.all([
            messageComponent.init(),
            toastComponent.init()
        ]).then(() => {
            log('消息编辑功能测试页面初始化完成');
            setupEventListeners();
        }).catch(error => {
            log(`初始化失败: ${error.message}`, 'error');
        });
        
        // 设置事件监听
        function setupEventListeners() {
            // 监听消息更新事件
            messageComponent.on('messageUpdated', (updatedMessage) => {
                const editRecord = {
                    messageId: updatedMessage.id,
                    timestamp: Date.now(),
                    editedAt: updatedMessage.editedAt,
                    content: updatedMessage.content
                };
                
                editHistory.push(editRecord);
                updateEditHistory();
                
                log(`消息已更新: ${updatedMessage.id}`);
            });
            
            // 监听消息编辑事件
            messageComponent.on('messageEdit', (message) => {
                log(`开始编辑消息: ${message.id}`);
            });
        }
        
        // 日志函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        // 更新编辑历史显示
        function updateEditHistory() {
            const historyOutput = document.getElementById('editHistory');
            
            if (editHistory.length === 0) {
                historyOutput.textContent = '暂无编辑记录...';
                return;
            }
            
            const historyText = editHistory.map(record => {
                const time = new Date(record.timestamp).toLocaleTimeString();
                const editTime = new Date(record.editedAt).toLocaleTimeString();
                const preview = record.content.length > 50 
                    ? record.content.substring(0, 50) + '...'
                    : record.content;
                
                return `[${time}] 消息 ${record.messageId} 在 ${editTime} 被编辑\n内容: ${preview}`;
            }).join('\n\n');
            
            historyOutput.textContent = historyText;
            historyOutput.scrollTop = historyOutput.scrollHeight;
        }
        
        // 测试函数
        window.addSampleMessages = function() {
            const sampleMessages = [
                {
                    content: '这是一条可以编辑的用户消息，点击编辑按钮试试看！',
                    isUser: true
                },
                {
                    content: '这是AI的回复消息，AI消息不能编辑。',
                    isUser: false
                },
                {
                    content: '这是另一条用户消息，你可以编辑它的内容。',
                    isUser: true
                },
                {
                    content: '短消息',
                    isUser: true
                },
                {
                    content: `这是一条很长的用户消息，用来测试编辑器的自动高度调整功能。
                    
当你编辑这条消息时，编辑器会根据内容自动调整高度。

你可以添加更多行，编辑器会自动扩展。

试试看编辑这条消息，体验一下内联编辑的功能！`,
                    isUser: true
                }
            ];
            
            sampleMessages.forEach(messageData => {
                messageComponent.addMessage({
                    ...messageData,
                    type: MessageTypes.TEXT,
                    status: MessageStatus.SENT
                });
            });
            
            log(`添加了 ${sampleMessages.length} 条示例消息`);
        };
        
        window.addUserMessage = function() {
            const userMessages = [
                '这是一条新的用户消息，可以编辑',
                '请帮我解决这个问题',
                '我想了解更多信息',
                '这个功能很有用！'
            ];
            
            const randomMessage = userMessages[Math.floor(Math.random() * userMessages.length)];
            
            messageComponent.addMessage({
                content: randomMessage,
                isUser: true,
                type: MessageTypes.TEXT,
                status: MessageStatus.SENT
            });
            
            log(`添加用户消息: ${randomMessage}`);
        };
        
        window.addLongMessage = function() {
            const longMessage = `这是一条很长的测试消息，专门用来测试编辑功能。

这条消息包含多个段落和换行符，可以用来测试：
1. 编辑器的自动高度调整
2. 多行文本的编辑体验
3. 保存和取消功能
4. 键盘快捷键支持

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.

Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

试试编辑这条消息，体验完整的编辑功能！`;
            
            messageComponent.addMessage({
                content: longMessage,
                isUser: true,
                type: MessageTypes.TEXT,
                status: MessageStatus.SENT
            });
            
            log('添加长消息（测试编辑功能）');
        };
        
        window.clearMessages = function() {
            messageComponent.clearMessages();
            editHistory.length = 0;
            updateEditHistory();
            
            // 显示提示
            const messagesContainer = document.getElementById('messages');
            messagesContainer.innerHTML = `
                <div class="feature-demo">
                    消息已清空，点击"添加示例消息"按钮重新开始测试
                </div>
            `;
            
            log('清空所有消息');
        };
        
        window.clearLog = function() {
            document.getElementById('logOutput').textContent = '';
            document.getElementById('editHistory').textContent = '暂无编辑记录...';
            editHistory.length = 0;
        };
        
        log('消息编辑功能测试页面已加载');
    </script>
</body>
</html>
