<!DOCTYPE html>
<html lang="zh-CN" data-theme="auto" data-font-size="md">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题和字号设置测试</title>
    
    <!-- 导入样式文件 -->
    <link rel="stylesheet" href="./styles/variables.css">
    <link rel="stylesheet" href="./styles/components.css">
    <link rel="stylesheet" href="./styles/layout.css">
    <link rel="stylesheet" href="./styles/themes.css">
    
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .test-section h2 {
            margin-top: 0;
            margin-bottom: var(--spacing-md);
            color: var(--color-foreground);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xl);
        }
        
        .demo-panel {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-lg);
        }
        
        .sample-content {
            background: var(--color-surface-hover);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-lg);
            margin: var(--spacing-md) 0;
        }
        
        .sample-content h3 {
            margin-top: 0;
            margin-bottom: var(--spacing-md);
            color: var(--color-primary);
        }
        
        .sample-content p {
            margin-bottom: var(--spacing-sm);
            line-height: var(--line-height-normal);
        }
        
        .sample-content .highlight {
            background: var(--color-primary-background);
            color: var(--color-primary);
            padding: 2px 4px;
            border-radius: var(--radius-xs);
        }
        
        .control-panel {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            flex-wrap: wrap;
        }
        
        .status-display {
            background: var(--color-surface-hover);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-item {
            text-align: center;
        }
        
        .status-label {
            font-size: var(--font-size-sm);
            color: var(--color-foreground-muted);
            margin-bottom: var(--spacing-xs);
        }
        
        .status-value {
            font-weight: var(--font-weight-bold);
            color: var(--color-primary);
        }
        
        .log-output {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            font-family: monospace;
            font-size: var(--font-size-sm);
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>主题和字号设置测试</h1>
        
        <!-- 状态显示 -->
        <div class="status-display">
            <div class="status-item">
                <div class="status-label">当前主题</div>
                <div class="status-value" id="currentTheme">auto</div>
            </div>
            <div class="status-item">
                <div class="status-label">当前字号</div>
                <div class="status-value" id="currentFontSize">md</div>
            </div>
            <div class="status-item">
                <div class="status-label">系统主题</div>
                <div class="status-value" id="systemTheme">light</div>
            </div>
        </div>
        
        <!-- 控制面板 -->
        <div class="test-section">
            <h2>快速测试</h2>
            <div class="control-panel">
                <button class="btn btn-primary" onclick="testAllThemes()">测试所有主题</button>
                <button class="btn btn-secondary" onclick="testAllFontSizes()">测试所有字号</button>
                <button class="btn btn-secondary" onclick="resetToDefaults()">重置为默认</button>
                <button class="btn btn-secondary" onclick="randomizeSettings()">随机设置</button>
                <button class="btn btn-ghost" onclick="clearLog()">清空日志</button>
            </div>
        </div>
        
        <!-- 主要演示区域 -->
        <div class="test-section">
            <h2>设置组件演示</h2>
            <div class="demo-grid">
                <!-- 主题选择器 -->
                <div class="demo-panel">
                    <h3>主题选择器</h3>
                    <div id="themeSelectorDemo"></div>
                </div>
                
                <!-- 字号调整器 -->
                <div class="demo-panel">
                    <h3>字号调整器</h3>
                    <div id="fontSizeAdjusterDemo"></div>
                </div>
            </div>
        </div>
        
        <!-- 效果预览 -->
        <div class="test-section">
            <h2>效果预览</h2>
            <div class="sample-content">
                <h3>示例内容</h3>
                <p>这是一段示例文本，用来展示当前主题和字号的效果。</p>
                <p>您可以看到不同主题下的<span class="highlight">颜色变化</span>和不同字号下的<span class="highlight">大小变化</span>。</p>
                <p>这个预览区域会实时反映您的设置更改，帮助您找到最适合的显示效果。</p>
                
                <h4>各种UI元素预览：</h4>
                <div style="margin: var(--spacing-md) 0;">
                    <button class="btn btn-primary">主要按钮</button>
                    <button class="btn btn-secondary">次要按钮</button>
                    <button class="btn btn-ghost">幽灵按钮</button>
                </div>
                
                <div style="margin: var(--spacing-md) 0;">
                    <input type="text" class="input" placeholder="输入框示例" style="width: 200px;">
                    <select class="input" style="width: 120px; margin-left: var(--spacing-sm);">
                        <option>选项1</option>
                        <option>选项2</option>
                    </select>
                </div>
                
                <div style="margin: var(--spacing-md) 0;">
                    <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                    <span style="margin-left: var(--spacing-sm);">开关组件示例</span>
                </div>
            </div>
        </div>
        
        <!-- 操作日志 -->
        <div class="test-section">
            <h2>操作日志</h2>
            <div id="logOutput" class="log-output">
                操作日志将在这里显示...
            </div>
        </div>
        
        <!-- Toast容器 -->
        <div class="toast-container" id="toastContainer"></div>
    </div>
    
    <script type="module">
        import { Store } from './scripts/state/store.js';
        import { ThemeSelectorComponent } from './scripts/components/theme-selector.js';
        import { FontSizeAdjusterComponent } from './scripts/components/font-size-adjuster.js';
        import { ToastComponent } from './scripts/components/toast.js';
        import { EventManager } from './scripts/utils/events.js';
        import { globalThemeManager } from './scripts/utils/theme.js';
        
        // 初始化组件
        const store = new Store();
        const eventManager = new EventManager();
        const toastComponent = new ToastComponent();
        
        // 创建组件容器
        const themeSelectorContainer = document.getElementById('themeSelectorDemo');
        const fontSizeAdjusterContainer = document.getElementById('fontSizeAdjusterDemo');
        
        const themeSelectorComponent = new ThemeSelectorComponent(themeSelectorContainer);
        const fontSizeAdjusterComponent = new FontSizeAdjusterComponent(fontSizeAdjusterContainer);
        
        // 全局变量
        window.store = store;
        window.themeSelectorComponent = themeSelectorComponent;
        window.fontSizeAdjusterComponent = fontSizeAdjusterComponent;
        window.toastComponent = toastComponent;
        
        // 初始化组件
        Promise.all([
            themeSelectorComponent.init(),
            fontSizeAdjusterComponent.init(),
            toastComponent.init()
        ]).then(() => {
            log('主题和字号设置测试页面初始化完成');
            setupEventListeners();
            updateStatus();
            globalThemeManager.init();
        }).catch(error => {
            log(`初始化失败: ${error.message}`, 'error');
        });
        
        // 设置事件监听
        function setupEventListeners() {
            // 监听主题变化
            themeSelectorComponent.on('themeSelected', (data) => {
                log(`主题已切换: ${data.themeName} (${data.themeId})`);
                updateStatus();
            });
            
            // 监听字号变化
            fontSizeAdjusterComponent.on('fontSizeChanged', (data) => {
                log(`字号已调整: ${data.fontSize.name} (${data.fontSize.size})`);
                updateStatus();
            });
            
            // 监听系统主题变化
            document.addEventListener('themechange', (e) => {
                log(`系统主题变化: ${e.detail.theme}`);
                updateStatus();
            });
            
            // 监听系统字号变化
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'data-font-size') {
                        updateStatus();
                    }
                });
            });
            
            observer.observe(document.documentElement, {
                attributes: true,
                attributeFilter: ['data-font-size']
            });
        }
        
        // 更新状态显示
        function updateStatus() {
            const currentTheme = globalThemeManager.getTheme();
            const currentFontSize = document.documentElement.getAttribute('data-font-size') || 'md';
            const systemTheme = globalThemeManager.getSystemTheme();
            
            document.getElementById('currentTheme').textContent = getThemeLabel(currentTheme);
            document.getElementById('currentFontSize').textContent = getFontSizeLabel(currentFontSize);
            document.getElementById('systemTheme').textContent = getThemeLabel(systemTheme);
        }
        
        function getThemeLabel(theme) {
            const labels = {
                auto: '跟随系统',
                light: '浅色',
                dark: '深色',
                'high-contrast': '高对比度'
            };
            return labels[theme] || theme;
        }
        
        function getFontSizeLabel(fontSize) {
            const labels = {
                xs: '极小',
                sm: '小',
                md: '中等',
                lg: '大',
                xl: '极大',
                '2xl': '超大'
            };
            return labels[fontSize] || fontSize;
        }
        
        // 日志函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        // 测试函数
        window.testAllThemes = function() {
            const themes = ['light', 'dark', 'high-contrast', 'auto'];
            let index = 0;
            
            function switchTheme() {
                if (index < themes.length) {
                    themeSelectorComponent.setTheme(themes[index]);
                    log(`自动切换到主题: ${getThemeLabel(themes[index])}`);
                    index++;
                    setTimeout(switchTheme, 2000);
                } else {
                    log('所有主题测试完成');
                }
            }
            
            log('开始测试所有主题...');
            switchTheme();
        };
        
        window.testAllFontSizes = function() {
            const fontSizes = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
            let index = 0;
            
            function switchFontSize() {
                if (index < fontSizes.length) {
                    fontSizeAdjusterComponent.setFontSize(fontSizes[index]);
                    log(`自动切换到字号: ${getFontSizeLabel(fontSizes[index])}`);
                    index++;
                    setTimeout(switchFontSize, 1500);
                } else {
                    log('所有字号测试完成');
                }
            }
            
            log('开始测试所有字号...');
            switchFontSize();
        };
        
        window.resetToDefaults = function() {
            themeSelectorComponent.setTheme('auto');
            fontSizeAdjusterComponent.setFontSize('md');
            log('已重置为默认设置');
        };
        
        window.randomizeSettings = function() {
            const themes = ['auto', 'light', 'dark', 'high-contrast'];
            const fontSizes = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
            
            const randomTheme = themes[Math.floor(Math.random() * themes.length)];
            const randomFontSize = fontSizes[Math.floor(Math.random() * fontSizes.length)];
            
            themeSelectorComponent.setTheme(randomTheme);
            fontSizeAdjusterComponent.setFontSize(randomFontSize);
            
            log(`随机设置: 主题=${getThemeLabel(randomTheme)}, 字号=${getFontSizeLabel(randomFontSize)}`);
        };
        
        window.clearLog = function() {
            document.getElementById('logOutput').textContent = '';
        };
        
        log('主题和字号设置测试页面已加载');
    </script>
</body>
</html>
