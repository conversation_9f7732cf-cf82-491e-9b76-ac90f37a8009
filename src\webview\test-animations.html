<!DOCTYPE html>
<html lang="zh-CN" data-theme="auto" data-font-size="md">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动画和微交互测试</title>
    
    <!-- 导入样式文件 -->
    <link rel="stylesheet" href="./styles/variables.css">
    <link rel="stylesheet" href="./styles/components.css">
    <link rel="stylesheet" href="./styles/layout.css">
    <link rel="stylesheet" href="./styles/themes.css">
    
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .test-section h2 {
            margin-top: 0;
            margin-bottom: var(--spacing-md);
            color: var(--color-foreground);
        }
        
        .control-panel {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            flex-wrap: wrap;
        }
        
        .animation-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .demo-card {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-normal);
        }
        
        .demo-card:hover {
            border-color: var(--color-primary);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .demo-element {
            width: 60px;
            height: 60px;
            background: var(--color-primary);
            border-radius: var(--radius-md);
            margin: 0 auto var(--spacing-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-primary-foreground);
            font-weight: bold;
        }
        
        .button-demo {
            display: flex;
            gap: var(--spacing-md);
            flex-wrap: wrap;
            margin-bottom: var(--spacing-lg);
        }
        
        .input-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .notification-demo {
            position: relative;
            height: 200px;
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            overflow: hidden;
        }
        
        .message-demo {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            height: 300px;
            overflow-y: auto;
        }
        
        .demo-message {
            display: flex;
            margin-bottom: var(--spacing-md);
            gap: var(--spacing-md);
        }
        
        .demo-message-user {
            align-items: flex-end;
            max-width: 75%;
            margin-left: auto;
        }
        
        .demo-message-ai {
            align-items: flex-start;
            max-width: 90%;
        }
        
        .demo-message-content {
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            transition: all var(--transition-fast);
        }
        
        .demo-message-user .demo-message-content {
            background: var(--color-primary);
            color: var(--color-primary-foreground);
        }
        
        .demo-message-content:hover {
            transform: scale(1.01);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .performance-stats {
            background: var(--color-surface-hover);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            font-family: monospace;
            font-size: var(--font-size-sm);
        }
        
        .animation-controls {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
            flex-wrap: wrap;
        }
        
        .control-btn {
            padding: var(--spacing-xs) var(--spacing-sm);
            border: 1px solid var(--color-border);
            background: var(--color-surface);
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: var(--font-size-sm);
            transition: all var(--transition-fast);
        }
        
        .control-btn:hover {
            background: var(--color-surface-hover);
            transform: translateY(-1px);
        }
        
        .control-btn.active {
            background: var(--color-primary);
            color: var(--color-primary-foreground);
            border-color: var(--color-primary);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>动画和微交互测试</h1>
        
        <!-- 控制面板 -->
        <div class="test-section">
            <h2>测试控制</h2>
            <div class="control-panel">
                <button class="btn btn-primary" onclick="testAllAnimations()">测试所有动画</button>
                <button class="btn btn-secondary" onclick="testMicroInteractions()">测试微交互</button>
                <button class="btn btn-secondary" onclick="testPerformance()">性能测试</button>
                <button class="btn btn-secondary" onclick="toggleReducedMotion()">切换减少动画</button>
                <button class="btn btn-ghost" onclick="stopAllAnimations()">停止所有动画</button>
            </div>
            
            <div class="performance-stats" id="performanceStats">
                性能统计: 等待测试...
            </div>
        </div>
        
        <!-- 基础动画测试 -->
        <div class="test-section">
            <h2>基础动画效果</h2>
            <div class="animation-controls">
                <div class="control-btn active" onclick="setAnimationSpeed('normal')">正常速度</div>
                <div class="control-btn" onclick="setAnimationSpeed('fast')">快速</div>
                <div class="control-btn" onclick="setAnimationSpeed('slow')">慢速</div>
            </div>
            
            <div class="animation-demo">
                <div class="demo-card" onclick="testAnimation('fadeIn')">
                    <div class="demo-element" id="fadeInDemo">淡入</div>
                    <h3>淡入动画</h3>
                    <p>opacity: 0 → 1</p>
                </div>
                
                <div class="demo-card" onclick="testAnimation('slideInLeft')">
                    <div class="demo-element" id="slideInLeftDemo">←</div>
                    <h3>左滑入</h3>
                    <p>从左侧滑入</p>
                </div>
                
                <div class="demo-card" onclick="testAnimation('slideInRight')">
                    <div class="demo-element" id="slideInRightDemo">→</div>
                    <h3>右滑入</h3>
                    <p>从右侧滑入</p>
                </div>
                
                <div class="demo-card" onclick="testAnimation('slideInUp')">
                    <div class="demo-element" id="slideInUpDemo">↑</div>
                    <h3>上滑入</h3>
                    <p>从下方滑入</p>
                </div>
                
                <div class="demo-card" onclick="testAnimation('scaleIn')">
                    <div class="demo-element" id="scaleInDemo">⚡</div>
                    <h3>缩放入</h3>
                    <p>从小到大</p>
                </div>
                
                <div class="demo-card" onclick="testAnimation('bounceIn')">
                    <div class="demo-element" id="bounceInDemo">🎾</div>
                    <h3>弹跳入</h3>
                    <p>弹性进入</p>
                </div>
                
                <div class="demo-card" onclick="testAnimation('shake')">
                    <div class="demo-element" id="shakeDemo">📳</div>
                    <h3>摇摆</h3>
                    <p>左右摇摆</p>
                </div>
                
                <div class="demo-card" onclick="testAnimation('pulse')">
                    <div class="demo-element" id="pulseDemo">💓</div>
                    <h3>脉冲</h3>
                    <p>缩放脉冲</p>
                </div>
            </div>
        </div>
        
        <!-- 按钮微交互测试 -->
        <div class="test-section">
            <h2>按钮微交互</h2>
            <div class="button-demo">
                <button class="btn btn-primary hover-lift">悬停上升</button>
                <button class="btn btn-secondary hover-scale">悬停缩放</button>
                <button class="btn btn-ghost hover-glow">悬停发光</button>
                <button class="btn btn-primary" onclick="testRipple(this)">波纹效果</button>
                <button class="btn btn-secondary focus-ring">焦点环</button>
                <button class="btn btn-ghost loading" id="loadingBtn">加载状态</button>
            </div>
        </div>
        
        <!-- 输入框交互测试 -->
        <div class="test-section">
            <h2>输入框交互</h2>
            <div class="input-demo">
                <input type="text" class="input" placeholder="普通输入框">
                <input type="text" class="input focus-ring" placeholder="带焦点环的输入框">
                <textarea class="input" placeholder="文本区域" rows="3"></textarea>
                <input type="text" class="input hover-scale" placeholder="悬停缩放输入框">
            </div>
        </div>
        
        <!-- 消息动画测试 -->
        <div class="test-section">
            <h2>消息动画</h2>
            <div class="animation-controls">
                <button class="btn btn-primary" onclick="addDemoMessage(true)">添加用户消息</button>
                <button class="btn btn-secondary" onclick="addDemoMessage(false)">添加AI消息</button>
                <button class="btn btn-ghost" onclick="clearDemoMessages()">清空消息</button>
            </div>
            
            <div class="message-demo" id="messageDemoContainer">
                <!-- 演示消息将在这里显示 -->
            </div>
        </div>
        
        <!-- 通知动画测试 -->
        <div class="test-section">
            <h2>通知动画</h2>
            <div class="animation-controls">
                <button class="btn btn-primary" onclick="showDemoNotification('success')">成功通知</button>
                <button class="btn btn-secondary" onclick="showDemoNotification('error')">错误通知</button>
                <button class="btn btn-ghost" onclick="showDemoNotification('warning')">警告通知</button>
                <button class="btn btn-primary" onclick="showDemoNotification('info')">信息通知</button>
                <button class="btn btn-secondary" onclick="showDemoNotification('loading')">加载通知</button>
            </div>
            
            <div class="notification-demo" id="notificationDemo">
                <!-- 演示通知将在这里显示 -->
            </div>
        </div>
        
        <!-- Toast容器 -->
        <div class="toast-container" id="toastContainer"></div>
    </div>
    
    <script type="module">
        import { globalAnimationManager, AnimationTypes, MicroInteractions, AnimationPerformance } from './scripts/utils/animations.js';
        import { globalNotificationManager } from './scripts/components/notification-manager.js';
        import { ToastComponent } from './scripts/components/toast.js';
        
        // 全局变量
        window.animationManager = globalAnimationManager;
        window.currentAnimationSpeed = 'normal';
        window.messageCounter = 0;
        window.notificationManager = globalNotificationManager;
        window.toastComponent = new ToastComponent();
        
        // 初始化
        Promise.all([
            globalNotificationManager.init(),
            window.toastComponent.init()
        ]).then(() => {
            console.log('动画和微交互测试页面初始化完成');
            updatePerformanceStats();
            setupMicroInteractions();
        }).catch(error => {
            console.error('初始化失败:', error);
        });
        
        // 设置微交互
        function setupMicroInteractions() {
            // 为所有按钮添加波纹效果
            document.querySelectorAll('.btn').forEach(btn => {
                if (!btn.hasAttribute('data-ripple-added')) {
                    MicroInteractions.addRippleEffect(btn);
                    btn.setAttribute('data-ripple-added', 'true');
                }
            });
            
            // 为输入框添加焦点环
            document.querySelectorAll('.input').forEach(input => {
                if (!input.hasAttribute('data-focus-added')) {
                    MicroInteractions.addFocusRing(input);
                    input.setAttribute('data-focus-added', 'true');
                }
            });
        }
        
        // 测试动画
        window.testAnimation = function(type) {
            const elementId = type + 'Demo';
            const element = document.getElementById(elementId);
            
            if (!element) return;
            
            const animationType = AnimationTypes[type.toUpperCase()];
            if (!animationType) return;
            
            const duration = currentAnimationSpeed === 'fast' ? 150 : 
                           currentAnimationSpeed === 'slow' ? 500 : 250;
            
            globalAnimationManager.animate(element, animationType, {
                duration,
                onStart: () => {
                    element.style.opacity = '0';
                    element.style.transform = '';
                },
                onComplete: () => {
                    element.style.opacity = '';
                    element.style.transform = '';
                }
            });
            
            updatePerformanceStats();
        };
        
        // 设置动画速度
        window.setAnimationSpeed = function(speed) {
            currentAnimationSpeed = speed;
            
            // 更新按钮状态
            document.querySelectorAll('.animation-controls .control-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        };
        
        // 测试所有动画
        window.testAllAnimations = function() {
            const animations = ['fadeIn', 'slideInLeft', 'slideInRight', 'slideInUp', 'scaleIn', 'bounceIn', 'shake', 'pulse'];
            
            animations.forEach((animation, index) => {
                setTimeout(() => {
                    testAnimation(animation);
                }, index * 300);
            });
        };
        
        // 测试微交互
        window.testMicroInteractions = function() {
            // 模拟悬停效果
            document.querySelectorAll('.hover-lift, .hover-scale, .hover-glow').forEach(element => {
                element.style.transform = element.classList.contains('hover-lift') ? 'translateY(-2px)' :
                                         element.classList.contains('hover-scale') ? 'scale(1.02)' : '';
                element.style.boxShadow = element.classList.contains('hover-glow') ? '0 0 20px rgba(var(--color-primary-rgb), 0.3)' : '';
                
                setTimeout(() => {
                    element.style.transform = '';
                    element.style.boxShadow = '';
                }, 1000);
            });
        };
        
        // 测试波纹效果
        window.testRipple = function(button) {
            // 波纹效果已经通过事件监听器自动触发
            console.log('波纹效果已触发');
        };
        
        // 添加演示消息
        window.addDemoMessage = function(isUser) {
            const container = document.getElementById('messageDemoContainer');
            const messageId = ++messageCounter;
            
            const messageElement = document.createElement('div');
            messageElement.className = `demo-message ${isUser ? 'demo-message-user' : 'demo-message-ai'}`;
            messageElement.innerHTML = `
                <div class="demo-message-content">
                    ${isUser ? '用户' : 'AI'}消息 #${messageId}
                </div>
            `;
            
            container.appendChild(messageElement);
            
            // 添加进入动画
            const animationType = isUser ? AnimationTypes.SLIDE_IN_RIGHT : AnimationTypes.SLIDE_IN_LEFT;
            globalAnimationManager.animate(messageElement, animationType, {
                duration: 300
            });
            
            // 自动滚动到底部
            container.scrollTop = container.scrollHeight;
            
            updatePerformanceStats();
        };
        
        // 清空演示消息
        window.clearDemoMessages = function() {
            const container = document.getElementById('messageDemoContainer');
            container.innerHTML = '';
            messageCounter = 0;
        };
        
        // 显示演示通知
        window.showDemoNotification = function(type) {
            const messages = {
                success: '操作成功完成！',
                error: '发生了一个错误',
                warning: '这是一个警告',
                info: '这是一条信息',
                loading: '正在处理中...'
            };
            
            globalNotificationManager.show({
                type,
                message: messages[type] || '通知消息',
                duration: type === 'loading' ? 3000 : 2000
            });
        };
        
        // 性能测试
        window.testPerformance = function() {
            const startTime = performance.now();
            let animationCount = 0;
            
            // 创建多个动画
            for (let i = 0; i < 10; i++) {
                setTimeout(() => {
                    testAnimation('fadeIn');
                    animationCount++;
                    
                    if (animationCount === 10) {
                        const endTime = performance.now();
                        const duration = endTime - startTime;
                        console.log(`性能测试完成: ${duration.toFixed(2)}ms`);
                        updatePerformanceStats();
                    }
                }, i * 100);
            }
        };
        
        // 切换减少动画
        window.toggleReducedMotion = function() {
            const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
            const currentState = mediaQuery.matches;
            
            // 模拟切换（实际上无法直接修改系统设置）
            document.body.style.setProperty('--transition-fast', currentState ? '150ms ease-in-out' : '0.01ms');
            document.body.style.setProperty('--transition-normal', currentState ? '250ms ease-in-out' : '0.01ms');
            document.body.style.setProperty('--transition-slow', currentState ? '350ms ease-in-out' : '0.01ms');
            
            window.toastComponent.show({
                type: 'info',
                message: `动画已${currentState ? '启用' : '禁用'}`
            });
        };
        
        // 停止所有动画
        window.stopAllAnimations = function() {
            globalAnimationManager.stopAllAnimations();
            updatePerformanceStats();
        };
        
        // 更新性能统计
        function updatePerformanceStats() {
            const stats = document.getElementById('performanceStats');
            const activeAnimations = globalAnimationManager.getActiveAnimationCount();
            const supportsHardwareAcceleration = AnimationPerformance.supportsHardwareAcceleration();
            const shouldReduceMotion = AnimationPerformance.shouldReduceMotion();
            
            stats.innerHTML = `
活跃动画数量: ${activeAnimations}
硬件加速支持: ${supportsHardwareAcceleration ? '是' : '否'}
减少动画偏好: ${shouldReduceMotion ? '是' : '否'}
当前动画速度: ${currentAnimationSpeed}
            `.trim();
        }
        
        // 定期更新性能统计
        setInterval(updatePerformanceStats, 1000);
        
        console.log('动画和微交互测试页面已加载');
    </script>
</body>
</html>
