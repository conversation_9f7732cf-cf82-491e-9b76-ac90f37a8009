<!DOCTYPE html>
<html lang="zh-CN" data-theme="auto" data-font-size="md">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息折叠功能测试</title>
    
    <!-- 导入样式文件 -->
    <link rel="stylesheet" href="./styles/variables.css">
    <link rel="stylesheet" href="./styles/components.css">
    <link rel="stylesheet" href="./styles/layout.css">
    <link rel="stylesheet" href="./styles/themes.css">
    
    <style>
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .test-section h2 {
            margin-top: 0;
            margin-bottom: var(--spacing-md);
            color: var(--color-foreground);
        }
        
        .control-panel {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            flex-wrap: wrap;
        }
        
        .demo-messages {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            max-height: 500px;
            overflow-y: auto;
        }
        
        .instructions {
            background: var(--color-surface-hover);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .instructions h3 {
            margin-top: 0;
            margin-bottom: var(--spacing-sm);
            color: var(--color-primary);
        }
        
        .instructions ul {
            margin: 0;
            padding-left: var(--spacing-xl);
        }
        
        .instructions li {
            margin-bottom: var(--spacing-xs);
        }
        
        .feature-demo {
            border: 2px dashed var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin: var(--spacing-md) 0;
            text-align: center;
            color: var(--color-foreground-muted);
        }
        
        .stats-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .stat-card {
            background: var(--color-surface-hover);
            padding: var(--spacing-md);
            border-radius: var(--radius-sm);
            text-align: center;
        }
        
        .stat-value {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-bold);
            color: var(--color-primary);
        }
        
        .stat-label {
            font-size: var(--font-size-sm);
            color: var(--color-foreground-muted);
            margin-top: var(--spacing-xs);
        }
        
        .log-output {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            font-family: monospace;
            font-size: var(--font-size-sm);
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>消息折叠功能测试</h1>
        
        <!-- 使用说明 -->
        <div class="instructions">
            <h3>📋 折叠功能使用说明</h3>
            <ul>
                <li><strong>自动折叠</strong>：超过5行的消息会自动折叠，显示前5行内容</li>
                <li><strong>展开按钮</strong>：点击"展开"按钮查看完整内容</li>
                <li><strong>收起按钮</strong>：展开后点击"收起"按钮重新折叠</li>
                <li><strong>行数指示器</strong>：右上角显示消息的总行数</li>
                <li><strong>渐变效果</strong>：折叠状态下底部有渐变遮罩效果</li>
                <li><strong>动画过渡</strong>：展开和收起都有平滑的动画效果</li>
            </ul>
        </div>
        
        <!-- 控制面板 -->
        <div class="test-section">
            <h2>测试控制</h2>
            <div class="control-panel">
                <button class="btn btn-primary" onclick="addVariousMessages()">添加各种长度消息</button>
                <button class="btn btn-secondary" onclick="addShortMessage()">添加短消息</button>
                <button class="btn btn-secondary" onclick="addMediumMessage()">添加中等消息</button>
                <button class="btn btn-secondary" onclick="addLongMessage()">添加长消息</button>
                <button class="btn btn-secondary" onclick="addVeryLongMessage()">添加超长消息</button>
                <button class="btn btn-secondary" onclick="clearMessages()">清空消息</button>
                <button class="btn btn-ghost" onclick="toggleAllMessages()">全部展开/收起</button>
            </div>
        </div>
        
        <!-- 统计信息 -->
        <div class="test-section">
            <h2>折叠统计</h2>
            <div class="stats-display" id="statsDisplay">
                <!-- 统计信息将在这里显示 -->
            </div>
        </div>
        
        <!-- 功能演示 -->
        <div class="test-section">
            <h2>折叠功能演示</h2>
            <div class="demo-messages" id="messages">
                <div class="feature-demo">
                    点击上方"添加各种长度消息"按钮开始测试折叠功能
                </div>
            </div>
        </div>
        
        <!-- 操作日志 -->
        <div class="test-section">
            <h2>操作日志</h2>
            <div class="control-panel">
                <button class="btn btn-ghost" onclick="clearLog()">清空日志</button>
            </div>
            <div id="logOutput" class="log-output">
                操作日志将在这里显示...
            </div>
        </div>
        
        <!-- Toast容器 -->
        <div class="toast-container" id="toastContainer"></div>
    </div>
    
    <script type="module">
        import { Store } from './scripts/state/store.js';
        import { MessageComponent } from './scripts/components/message.js';
        import { ToastComponent } from './scripts/components/toast.js';
        import { EventManager } from './scripts/utils/events.js';
        import { MessageTypes, MessageStatus } from './scripts/components/message-manager.js';
        
        // 初始化组件
        const store = new Store();
        const eventManager = new EventManager();
        const messageComponent = new MessageComponent(store, eventManager);
        const toastComponent = new ToastComponent();
        
        // 全局变量
        window.store = store;
        window.messageComponent = messageComponent;
        window.toastComponent = toastComponent;
        window.collapseStats = {
            total: 0,
            collapsed: 0,
            expanded: 0,
            toggleCount: 0
        };
        
        // 初始化组件
        Promise.all([
            messageComponent.init(),
            toastComponent.init()
        ]).then(() => {
            log('消息折叠功能测试页面初始化完成');
            setupEventListeners();
            updateStats();
        }).catch(error => {
            log(`初始化失败: ${error.message}`, 'error');
        });
        
        // 设置事件监听
        function setupEventListeners() {
            // 监听消息切换事件
            messageComponent.on('messageToggled', (data) => {
                collapseStats.toggleCount++;
                if (data.expanded) {
                    collapseStats.expanded++;
                    collapseStats.collapsed--;
                } else {
                    collapseStats.collapsed++;
                    collapseStats.expanded--;
                }
                
                updateStats();
                log(`消息 ${data.messageId} ${data.expanded ? '展开' : '收起'}`);
            });
            
            // 监听消息添加事件
            messageComponent.on('messageAdded', (message) => {
                collapseStats.total++;
                
                // 检查是否会被折叠
                setTimeout(() => {
                    const messageElement = document.querySelector(`[data-message-id="${message.id}"]`);
                    if (messageElement && messageElement.dataset.collapsible === 'true') {
                        collapseStats.collapsed++;
                    }
                    updateStats();
                }, 100);
            });
        }
        
        // 日志函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        // 更新统计信息
        function updateStats() {
            const statsDisplay = document.getElementById('statsDisplay');
            
            statsDisplay.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${collapseStats.total}</div>
                    <div class="stat-label">总消息数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${collapseStats.collapsed}</div>
                    <div class="stat-label">已折叠</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${collapseStats.expanded}</div>
                    <div class="stat-label">已展开</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${collapseStats.toggleCount}</div>
                    <div class="stat-label">切换次数</div>
                </div>
            `;
        }
        
        // 测试函数
        window.addVariousMessages = function() {
            const messages = [
                {
                    content: '这是一条短消息。',
                    description: '短消息（1行）'
                },
                {
                    content: '这是一条稍微长一点的消息，但仍然不会触发折叠功能。\n这是第二行内容。',
                    description: '中等消息（2行）'
                },
                {
                    content: `这是一条会触发折叠的长消息。
这是第二行内容。
这是第三行内容。
这是第四行内容。
这是第五行内容。
这是第六行内容，会被折叠。
这是第七行内容，也会被折叠。`,
                    description: '长消息（7行，会折叠）'
                },
                {
                    content: `这是一条超长的消息，用来测试折叠功能的极限情况。

这条消息包含多个段落和大量文本内容。

第一段：Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.

第二段：Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

第三段：Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.

第四段：Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

第五段：Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.

这是最后一段，用来确保消息足够长以充分测试折叠功能。`,
                    description: '超长消息（15+行，会折叠）'
                }
            ];
            
            messages.forEach((messageData, index) => {
                setTimeout(() => {
                    messageComponent.addMessage({
                        content: messageData.content,
                        isUser: index % 2 === 0,
                        type: MessageTypes.TEXT,
                        status: MessageStatus.SENT
                    });
                    
                    log(`添加${messageData.description}`);
                }, index * 200);
            });
        };
        
        window.addShortMessage = function() {
            const shortMessages = [
                '短消息',
                '这是一条短消息。',
                '简短回复。',
                'OK'
            ];
            
            const randomMessage = shortMessages[Math.floor(Math.random() * shortMessages.length)];
            
            messageComponent.addMessage({
                content: randomMessage,
                isUser: Math.random() > 0.5,
                type: MessageTypes.TEXT,
                status: MessageStatus.SENT
            });
            
            log(`添加短消息: ${randomMessage}`);
        };
        
        window.addMediumMessage = function() {
            const mediumMessage = `这是一条中等长度的消息。
它包含几行文本，但不会触发折叠功能。
这是第三行内容。
这是第四行内容。
这是第五行内容，刚好在折叠阈值边缘。`;
            
            messageComponent.addMessage({
                content: mediumMessage,
                isUser: Math.random() > 0.5,
                type: MessageTypes.TEXT,
                status: MessageStatus.SENT
            });
            
            log('添加中等长度消息（5行，边缘情况）');
        };
        
        window.addLongMessage = function() {
            const longMessage = `这是一条长消息，会触发折叠功能。
这是第二行内容。
这是第三行内容。
这是第四行内容。
这是第五行内容。
这是第六行内容，会被折叠。
这是第七行内容，也会被折叠。
这是第八行内容，继续被折叠。`;
            
            messageComponent.addMessage({
                content: longMessage,
                isUser: Math.random() > 0.5,
                type: MessageTypes.TEXT,
                status: MessageStatus.SENT
            });
            
            log('添加长消息（8行，会折叠）');
        };
        
        window.addVeryLongMessage = function() {
            const veryLongMessage = `这是一条超长消息，用来测试折叠功能的性能和用户体验。

这条消息包含大量文本内容，分为多个段落：

段落1：在软件开发中，用户界面的设计至关重要。一个好的界面不仅要美观，更要实用和易用。

段落2：消息折叠功能是提升用户体验的重要特性之一。它可以帮助用户更好地管理长文本内容。

段落3：当消息内容超过一定长度时，自动折叠可以保持界面的整洁，同时允许用户按需查看完整内容。

段落4：折叠功能的实现需要考虑多个方面：行数检测、动画效果、用户交互、性能优化等。

段落5：通过合理的设计和实现，折叠功能可以显著提升应用的可用性和用户满意度。

段落6：这个测试页面展示了折叠功能的各种特性，包括自动检测、平滑动画、状态指示等。

段落7：用户可以通过点击展开按钮查看完整内容，也可以随时收起以节省屏幕空间。

这是最后一段，感谢您测试这个折叠功能！`;
            
            messageComponent.addMessage({
                content: veryLongMessage,
                isUser: Math.random() > 0.5,
                type: MessageTypes.TEXT,
                status: MessageStatus.SENT
            });
            
            log('添加超长消息（20+行，会折叠）');
        };
        
        window.clearMessages = function() {
            messageComponent.clearMessages();
            
            // 重置统计
            collapseStats.total = 0;
            collapseStats.collapsed = 0;
            collapseStats.expanded = 0;
            collapseStats.toggleCount = 0;
            updateStats();
            
            // 显示提示
            const messagesContainer = document.getElementById('messages');
            messagesContainer.innerHTML = `
                <div class="feature-demo">
                    消息已清空，点击"添加各种长度消息"按钮重新开始测试
                </div>
            `;
            
            log('清空所有消息');
        };
        
        window.toggleAllMessages = function() {
            const expandButtons = document.querySelectorAll('.message-expand-btn');
            
            if (expandButtons.length === 0) {
                log('没有可折叠的消息', 'warning');
                return;
            }
            
            // 检查是否有已展开的消息
            const expandedMessages = document.querySelectorAll('.message-expanded');
            const shouldExpand = expandedMessages.length === 0;
            
            expandButtons.forEach((button, index) => {
                setTimeout(() => {
                    button.click();
                }, index * 100);
            });
            
            log(`${shouldExpand ? '展开' : '收起'}所有可折叠消息`);
        };
        
        window.clearLog = function() {
            document.getElementById('logOutput').textContent = '';
        };
        
        log('消息折叠功能测试页面已加载');
    </script>
</body>
</html>
