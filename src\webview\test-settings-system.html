<!DOCTYPE html>
<html lang="zh-CN" data-theme="auto" data-font-size="md">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置系统测试</title>
    
    <!-- 导入样式文件 -->
    <link rel="stylesheet" href="./styles/variables.css">
    <link rel="stylesheet" href="./styles/components.css">
    <link rel="stylesheet" href="./styles/layout.css">
    <link rel="stylesheet" href="./styles/themes.css">
    
    <style>
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .test-section h2 {
            margin-top: 0;
            margin-bottom: var(--spacing-md);
            color: var(--color-foreground);
        }
        
        .control-panel {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            flex-wrap: wrap;
        }
        
        .demo-area {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            min-height: 400px;
        }
        
        .settings-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .setting-card {
            background: var(--color-surface-hover);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
        }
        
        .setting-card h3 {
            margin-top: 0;
            margin-bottom: var(--spacing-sm);
            color: var(--color-primary);
            font-size: var(--font-size-md);
        }
        
        .setting-value {
            font-weight: var(--font-weight-bold);
            color: var(--color-foreground);
        }
        
        .setting-description {
            font-size: var(--font-size-sm);
            color: var(--color-foreground-muted);
            margin-top: var(--spacing-xs);
        }
        
        .log-output {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            font-family: monospace;
            font-size: var(--font-size-sm);
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .theme-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-sm);
            margin: var(--spacing-md) 0;
        }
        
        .theme-sample {
            padding: var(--spacing-md);
            border-radius: var(--radius-sm);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-fast);
        }
        
        .theme-sample:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .font-size-demo {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            margin: var(--spacing-md) 0;
        }
        
        .font-sample {
            padding: var(--spacing-sm);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            cursor: pointer;
            transition: all var(--transition-fast);
        }
        
        .font-sample:hover {
            background: var(--color-surface-hover);
        }
        
        .font-sample.active {
            border-color: var(--color-primary);
            background: var(--color-primary);
            color: var(--color-primary-foreground);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>设置系统测试</h1>
        
        <!-- 控制面板 -->
        <div class="test-section">
            <h2>测试控制</h2>
            <div class="control-panel">
                <button class="btn btn-primary" onclick="showSettings()">显示设置页面</button>
                <button class="btn btn-secondary" onclick="hideSettings()">隐藏设置页面</button>
                <button class="btn btn-secondary" onclick="resetAllSettings()">重置所有设置</button>
                <button class="btn btn-secondary" onclick="exportSettings()">导出设置</button>
                <button class="btn btn-secondary" onclick="importSettings()">导入设置</button>
                <button class="btn btn-ghost" onclick="refreshPreview()">刷新预览</button>
                <button class="btn btn-ghost" onclick="clearLog()">清空日志</button>
            </div>
        </div>
        
        <!-- 当前设置预览 -->
        <div class="test-section">
            <h2>当前设置预览</h2>
            <div class="settings-preview" id="settingsPreview">
                <!-- 设置卡片将在这里动态生成 -->
            </div>
        </div>
        
        <!-- 主题测试 -->
        <div class="test-section">
            <h2>主题切换测试</h2>
            <div class="theme-demo">
                <div class="theme-sample" style="background: #f8f9fa; color: #212529;" onclick="changeTheme('light')">
                    <strong>浅色主题</strong>
                    <div style="font-size: 12px; margin-top: 4px;">Light</div>
                </div>
                <div class="theme-sample" style="background: #212529; color: #f8f9fa;" onclick="changeTheme('dark')">
                    <strong>深色主题</strong>
                    <div style="font-size: 12px; margin-top: 4px;">Dark</div>
                </div>
                <div class="theme-sample" style="background: #000; color: #fff; border: 2px solid #fff;" onclick="changeTheme('high-contrast')">
                    <strong>高对比度</strong>
                    <div style="font-size: 12px; margin-top: 4px;">High Contrast</div>
                </div>
                <div class="theme-sample" style="background: linear-gradient(45deg, #f8f9fa 50%, #212529 50%); color: #007acc;" onclick="changeTheme('auto')">
                    <strong>跟随系统</strong>
                    <div style="font-size: 12px; margin-top: 4px;">Auto</div>
                </div>
            </div>
        </div>
        
        <!-- 字号测试 -->
        <div class="test-section">
            <h2>字号切换测试</h2>
            <div class="font-size-demo">
                <div class="font-sample" style="font-size: 11px;" onclick="changeFontSize('xs')">
                    极小字号 (XS) - 这是极小字号的示例文本
                </div>
                <div class="font-sample" style="font-size: 12px;" onclick="changeFontSize('sm')">
                    小字号 (SM) - 这是小字号的示例文本
                </div>
                <div class="font-sample" style="font-size: 14px;" onclick="changeFontSize('md')">
                    中等字号 (MD) - 这是中等字号的示例文本
                </div>
                <div class="font-sample" style="font-size: 16px;" onclick="changeFontSize('lg')">
                    大字号 (LG) - 这是大字号的示例文本
                </div>
                <div class="font-sample" style="font-size: 18px;" onclick="changeFontSize('xl')">
                    极大字号 (XL) - 这是极大字号的示例文本
                </div>
                <div class="font-sample" style="font-size: 20px;" onclick="changeFontSize('2xl')">
                    超大字号 (2XL) - 这是超大字号的示例文本
                </div>
            </div>
        </div>
        
        <!-- 设置演示区域 -->
        <div class="test-section">
            <h2>设置界面演示</h2>
            <div class="demo-area" id="settingsDemo">
                <!-- 设置界面将在这里显示 -->
                <div style="text-align: center; padding: 40px; color: var(--color-foreground-muted);">
                    点击"显示设置页面"按钮查看设置界面
                </div>
            </div>
        </div>
        
        <!-- 操作日志 -->
        <div class="test-section">
            <h2>操作日志</h2>
            <div id="logOutput" class="log-output">
                操作日志将在这里显示...
            </div>
        </div>
        
        <!-- Toast容器 -->
        <div class="toast-container" id="toastContainer"></div>
    </div>
    
    <script type="module">
        import { Store } from './scripts/state/store.js';
        import { SettingsComponent } from './scripts/components/settings.js';
        import { ToastComponent } from './scripts/components/toast.js';
        import { EventManager } from './scripts/utils/events.js';
        import { StorageUtils } from './scripts/utils/storage.js';
        import { globalThemeManager } from './scripts/utils/theme.js';
        
        // 初始化组件
        const store = new Store();
        const eventManager = new EventManager();
        const settingsComponent = new SettingsComponent(store, eventManager);
        const toastComponent = new ToastComponent();
        
        // 全局变量
        window.store = store;
        window.settingsComponent = settingsComponent;
        window.toastComponent = toastComponent;
        window.eventManager = eventManager;
        
        // 初始化组件
        Promise.all([
            settingsComponent.init(),
            toastComponent.init()
        ]).then(() => {
            log('设置系统测试页面初始化完成');
            setupEventListeners();
            refreshPreview();
            globalThemeManager.init();
        }).catch(error => {
            log(`初始化失败: ${error.message}`, 'error');
        });
        
        // 设置事件监听
        function setupEventListeners() {
            // 监听设置变化
            settingsComponent.on('settingChanged', (data) => {
                log(`设置已更改: ${data.name} = ${data.value}`);
                refreshPreview();
            });
            
            // 监听主题变化
            document.addEventListener('themechange', (e) => {
                log(`主题已切换: ${e.detail.theme}`);
                refreshPreview();
            });
            
            // 监听设置显示/隐藏
            settingsComponent.on('shown', () => {
                log('设置页面已显示');
            });
            
            settingsComponent.on('hidden', () => {
                log('设置页面已隐藏');
            });
        }
        
        // 日志函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        // 刷新设置预览
        function refreshPreview() {
            const settings = store.getState('settings') || {};
            const preview = document.getElementById('settingsPreview');
            
            preview.innerHTML = `
                <div class="setting-card">
                    <h3>主题设置</h3>
                    <div class="setting-value">${getThemeLabel(settings.theme || 'auto')}</div>
                    <div class="setting-description">当前应用的主题模式</div>
                </div>
                <div class="setting-card">
                    <h3>字体大小</h3>
                    <div class="setting-value">${getFontSizeLabel(settings.fontSize || 'md')}</div>
                    <div class="setting-description">界面文字的显示大小</div>
                </div>
                <div class="setting-card">
                    <h3>自动滚动</h3>
                    <div class="setting-value">${settings.autoScroll !== false ? '开启' : '关闭'}</div>
                    <div class="setting-description">新消息时自动滚动到底部</div>
                </div>
                <div class="setting-card">
                    <h3>显示时间戳</h3>
                    <div class="setting-value">${settings.showTimestamps !== false ? '显示' : '隐藏'}</div>
                    <div class="setting-description">在消息中显示时间信息</div>
                </div>
                <div class="setting-card">
                    <h3>启用通知</h3>
                    <div class="setting-value">${settings.enableNotifications !== false ? '启用' : '禁用'}</div>
                    <div class="setting-description">显示操作结果通知</div>
                </div>
                <div class="setting-card">
                    <h3>最大历史记录</h3>
                    <div class="setting-value">${settings.maxHistorySize || 1000} 条</div>
                    <div class="setting-description">保存的聊天记录数量限制</div>
                </div>
            `;
            
            // 更新字号示例的激活状态
            updateFontSizeDemo(settings.fontSize || 'md');
        }
        
        function getThemeLabel(theme) {
            const labels = {
                auto: '跟随系统',
                light: '浅色主题',
                dark: '深色主题',
                'high-contrast': '高对比度'
            };
            return labels[theme] || '未知';
        }
        
        function getFontSizeLabel(fontSize) {
            const labels = {
                xs: '极小',
                sm: '小',
                md: '中等',
                lg: '大',
                xl: '极大',
                '2xl': '超大'
            };
            return labels[fontSize] || '未知';
        }
        
        function updateFontSizeDemo(currentSize) {
            const samples = document.querySelectorAll('.font-sample');
            samples.forEach(sample => {
                sample.classList.remove('active');
            });
            
            const sizeMap = { xs: 0, sm: 1, md: 2, lg: 3, xl: 4, '2xl': 5 };
            const index = sizeMap[currentSize];
            if (index !== undefined && samples[index]) {
                samples[index].classList.add('active');
            }
        }
        
        // 测试函数
        window.showSettings = function() {
            settingsComponent.show();
            log('显示设置页面');
        };
        
        window.hideSettings = function() {
            settingsComponent.hide();
            log('隐藏设置页面');
        };
        
        window.resetAllSettings = function() {
            settingsComponent.resetSettings();
            log('重置所有设置');
        };
        
        window.exportSettings = function() {
            settingsComponent.exportSettings();
            log('导出设置');
        };
        
        window.importSettings = function() {
            settingsComponent.importSettings();
            log('触发导入设置');
        };
        
        window.changeTheme = function(theme) {
            globalThemeManager.setTheme(theme);
            store.setState('settings', { ...store.getState('settings'), theme });
            log(`切换主题: ${theme}`);
        };
        
        window.changeFontSize = function(fontSize) {
            document.documentElement.setAttribute('data-font-size', fontSize);
            store.setState('settings', { ...store.getState('settings'), fontSize });
            log(`切换字号: ${fontSize}`);
        };
        
        window.refreshPreview = refreshPreview;
        
        window.clearLog = function() {
            document.getElementById('logOutput').textContent = '';
        };
        
        log('设置系统测试页面已加载');
    </script>
</body>
</html>
