<!DOCTYPE html>
<html lang="zh-CN" data-theme="auto" data-font-size="md">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置数据持久化测试</title>
    
    <!-- 导入样式文件 -->
    <link rel="stylesheet" href="./styles/variables.css">
    <link rel="stylesheet" href="./styles/components.css">
    <link rel="stylesheet" href="./styles/layout.css">
    <link rel="stylesheet" href="./styles/themes.css">
    
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .test-section h2 {
            margin-top: 0;
            margin-bottom: var(--spacing-md);
            color: var(--color-foreground);
        }
        
        .control-panel {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            flex-wrap: wrap;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xl);
        }
        
        .demo-panel {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-lg);
        }
        
        .settings-display {
            background: var(--color-surface-hover);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            font-family: monospace;
            font-size: var(--font-size-sm);
            max-height: 300px;
            overflow-y: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: var(--spacing-xs);
        }
        
        .status-indicator.success {
            background: var(--color-success);
        }
        
        .status-indicator.error {
            background: var(--color-error);
        }
        
        .status-indicator.warning {
            background: var(--color-warning);
        }
        
        .log-output {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            font-family: monospace;
            font-size: var(--font-size-sm);
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .test-input {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }
        
        .test-input input {
            flex: 1;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .stat-card {
            background: var(--color-surface-hover);
            padding: var(--spacing-md);
            border-radius: var(--radius-sm);
            text-align: center;
        }
        
        .stat-value {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-bold);
            color: var(--color-primary);
        }
        
        .stat-label {
            font-size: var(--font-size-sm);
            color: var(--color-foreground-muted);
            margin-top: var(--spacing-xs);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>设置数据持久化测试</h1>
        
        <!-- 控制面板 -->
        <div class="test-section">
            <h2>测试控制</h2>
            <div class="control-panel">
                <button class="btn btn-primary" onclick="testBasicPersistence()">基础持久化测试</button>
                <button class="btn btn-secondary" onclick="testBatchUpdate()">批量更新测试</button>
                <button class="btn btn-secondary" onclick="testValidation()">验证测试</button>
                <button class="btn btn-secondary" onclick="testMigration()">迁移测试</button>
                <button class="btn btn-secondary" onclick="testWatchers()">监听器测试</button>
                <button class="btn btn-secondary" onclick="simulateStorageError()">模拟存储错误</button>
                <button class="btn btn-ghost" onclick="clearAllData()">清空所有数据</button>
                <button class="btn btn-ghost" onclick="clearLog()">清空日志</button>
            </div>
        </div>
        
        <!-- 统计信息 -->
        <div class="test-section">
            <h2>持久化统计</h2>
            <div class="stats-grid" id="statsGrid">
                <!-- 统计卡片将在这里动态生成 -->
            </div>
        </div>
        
        <!-- 主要演示区域 -->
        <div class="test-section">
            <h2>设置数据演示</h2>
            <div class="demo-grid">
                <!-- 当前设置 -->
                <div class="demo-panel">
                    <h3>当前设置</h3>
                    <div id="currentSettings" class="settings-display">
                        <!-- 当前设置将在这里显示 -->
                    </div>
                    <button class="btn btn-secondary" onclick="refreshSettings()">刷新设置</button>
                </div>
                
                <!-- 存储状态 -->
                <div class="demo-panel">
                    <h3>存储状态</h3>
                    <div id="storageStatus" class="settings-display">
                        <!-- 存储状态将在这里显示 -->
                    </div>
                    <button class="btn btn-secondary" onclick="refreshStorage()">刷新存储</button>
                </div>
            </div>
        </div>
        
        <!-- 手动测试 -->
        <div class="test-section">
            <h2>手动测试</h2>
            <div class="test-input">
                <input type="text" id="testKey" class="input" placeholder="设置键 (如: theme)">
                <input type="text" id="testValue" class="input" placeholder="设置值 (如: dark)">
                <button class="btn btn-primary" onclick="setTestSetting()">设置</button>
                <button class="btn btn-secondary" onclick="getTestSetting()">获取</button>
            </div>
            
            <div class="control-panel">
                <button class="btn btn-secondary" onclick="exportSettings()">导出设置</button>
                <button class="btn btn-secondary" onclick="importSettings()">导入设置</button>
                <button class="btn btn-secondary" onclick="resetSettings()">重置设置</button>
            </div>
        </div>
        
        <!-- 操作日志 -->
        <div class="test-section">
            <h2>操作日志</h2>
            <div id="logOutput" class="log-output">
                操作日志将在这里显示...
            </div>
        </div>
        
        <!-- Toast容器 -->
        <div class="toast-container" id="toastContainer"></div>
    </div>
    
    <script type="module">
        import { globalSettingsPersistence } from './scripts/utils/settings-persistence.js';
        import { StorageUtils } from './scripts/utils/storage.js';
        import { ToastComponent } from './scripts/components/toast.js';
        
        // 初始化组件
        const toastComponent = new ToastComponent();
        
        // 全局变量
        window.settingsPersistence = globalSettingsPersistence;
        window.toastComponent = toastComponent;
        window.testStats = {
            totalOperations: 0,
            successfulOperations: 0,
            failedOperations: 0,
            watcherTriggers: 0,
            syncOperations: 0
        };
        
        // 初始化
        Promise.all([
            globalSettingsPersistence.init(),
            toastComponent.init()
        ]).then(() => {
            log('设置数据持久化测试页面初始化完成');
            setupEventListeners();
            refreshSettings();
            refreshStorage();
            updateStats();
        }).catch(error => {
            log(`初始化失败: ${error.message}`, 'error');
        });
        
        // 设置事件监听
        function setupEventListeners() {
            const eventManager = globalSettingsPersistence.getEventManager();
            
            // 监听设置变化
            eventManager.on('settingChanged', (data) => {
                testStats.watcherTriggers++;
                log(`设置变化: ${data.key} = ${JSON.stringify(data.newValue)}`);
                refreshSettings();
                updateStats();
            });
            
            // 监听设置同步
            eventManager.on('settingsSynced', (data) => {
                testStats.syncOperations++;
                log(`设置已同步到存储 (${new Date(data.timestamp).toLocaleTimeString()})`);
                refreshStorage();
                updateStats();
            });
            
            // 监听同步错误
            eventManager.on('settingsSyncError', (error) => {
                testStats.failedOperations++;
                log(`设置同步失败: ${error.message}`, 'error');
                updateStats();
            });
            
            // 监听设置加载
            eventManager.on('settingsLoaded', (settings) => {
                log(`设置已加载: ${Object.keys(settings).length} 个设置项`);
                refreshSettings();
            });
            
            // 监听设置重置
            eventManager.on('settingsReset', () => {
                log('设置已重置为默认值');
                refreshSettings();
                refreshStorage();
            });
            
            // 监听设置导入
            eventManager.on('settingsImported', (data) => {
                log(`设置已导入 (版本: ${data.importedVersion})`);
                refreshSettings();
                refreshStorage();
            });
        }
        
        // 日志函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        // 更新统计信息
        function updateStats() {
            const statsGrid = document.getElementById('statsGrid');
            
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${testStats.totalOperations}</div>
                    <div class="stat-label">总操作数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${testStats.successfulOperations}</div>
                    <div class="stat-label">成功操作</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${testStats.failedOperations}</div>
                    <div class="stat-label">失败操作</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${testStats.watcherTriggers}</div>
                    <div class="stat-label">监听器触发</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${testStats.syncOperations}</div>
                    <div class="stat-label">同步操作</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${localStorage.length}</div>
                    <div class="stat-label">存储项数</div>
                </div>
            `;
        }
        
        // 刷新设置显示
        function refreshSettings() {
            const settings = globalSettingsPersistence.get();
            const display = document.getElementById('currentSettings');
            
            display.innerHTML = `<pre>${JSON.stringify(settings, null, 2)}</pre>`;
        }
        
        // 刷新存储显示
        function refreshStorage() {
            const storageData = {};
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                try {
                    const value = JSON.parse(localStorage.getItem(key));
                    storageData[key] = value;
                } catch (e) {
                    storageData[key] = localStorage.getItem(key);
                }
            }
            
            const display = document.getElementById('storageStatus');
            display.innerHTML = `<pre>${JSON.stringify(storageData, null, 2)}</pre>`;
        }
        
        // 测试函数
        window.testBasicPersistence = async function() {
            log('开始基础持久化测试...');
            testStats.totalOperations++;
            
            try {
                // 设置一个值
                await globalSettingsPersistence.set('testSetting', 'testValue');
                
                // 获取值
                const value = globalSettingsPersistence.get('testSetting');
                
                if (value === 'testValue') {
                    testStats.successfulOperations++;
                    log('基础持久化测试通过', 'success');
                } else {
                    throw new Error(`期望 'testValue'，实际得到 '${value}'`);
                }
                
            } catch (error) {
                testStats.failedOperations++;
                log(`基础持久化测试失败: ${error.message}`, 'error');
            }
            
            updateStats();
        };
        
        window.testBatchUpdate = async function() {
            log('开始批量更新测试...');
            testStats.totalOperations++;
            
            try {
                const batchSettings = {
                    theme: 'dark',
                    fontSize: 'lg',
                    autoScroll: false,
                    testBatch: true
                };
                
                await globalSettingsPersistence.set(batchSettings);
                
                // 验证所有设置
                let allCorrect = true;
                for (const [key, expectedValue] of Object.entries(batchSettings)) {
                    const actualValue = globalSettingsPersistence.get(key);
                    if (actualValue !== expectedValue) {
                        allCorrect = false;
                        break;
                    }
                }
                
                if (allCorrect) {
                    testStats.successfulOperations++;
                    log('批量更新测试通过', 'success');
                } else {
                    throw new Error('批量设置验证失败');
                }
                
            } catch (error) {
                testStats.failedOperations++;
                log(`批量更新测试失败: ${error.message}`, 'error');
            }
            
            updateStats();
        };
        
        window.testValidation = async function() {
            log('开始验证测试...');
            testStats.totalOperations++;
            
            try {
                // 测试无效主题
                await globalSettingsPersistence.set('theme', 'invalid-theme');
                const theme = globalSettingsPersistence.get('theme');
                
                // 应该回退到默认值
                if (theme === 'auto') {
                    testStats.successfulOperations++;
                    log('验证测试通过 - 无效值被正确处理', 'success');
                } else {
                    throw new Error(`期望默认主题 'auto'，实际得到 '${theme}'`);
                }
                
            } catch (error) {
                testStats.failedOperations++;
                log(`验证测试失败: ${error.message}`, 'error');
            }
            
            updateStats();
        };
        
        window.testWatchers = function() {
            log('开始监听器测试...');
            testStats.totalOperations++;
            
            try {
                let changeCount = 0;
                
                // 设置监听器
                const unwatch = globalSettingsPersistence.watch('testWatcher', (data) => {
                    changeCount++;
                    log(`监听器触发: ${data.key} = ${data.newValue}`);
                });
                
                // 触发变化
                globalSettingsPersistence.set('testWatcher', 'value1');
                globalSettingsPersistence.set('testWatcher', 'value2');
                
                setTimeout(() => {
                    if (changeCount >= 2) {
                        testStats.successfulOperations++;
                        log('监听器测试通过', 'success');
                    } else {
                        testStats.failedOperations++;
                        log(`监听器测试失败: 期望至少2次触发，实际${changeCount}次`, 'error');
                    }
                    
                    // 清理监听器
                    unwatch();
                    updateStats();
                }, 100);
                
            } catch (error) {
                testStats.failedOperations++;
                log(`监听器测试失败: ${error.message}`, 'error');
                updateStats();
            }
        };
        
        window.setTestSetting = async function() {
            const key = document.getElementById('testKey').value.trim();
            const value = document.getElementById('testValue').value.trim();
            
            if (!key) {
                log('请输入设置键', 'error');
                return;
            }
            
            try {
                await globalSettingsPersistence.set(key, value);
                log(`设置 ${key} = ${value}`, 'success');
                testStats.totalOperations++;
                testStats.successfulOperations++;
            } catch (error) {
                log(`设置失败: ${error.message}`, 'error');
                testStats.totalOperations++;
                testStats.failedOperations++;
            }
            
            updateStats();
        };
        
        window.getTestSetting = function() {
            const key = document.getElementById('testKey').value.trim();
            
            if (!key) {
                log('请输入设置键', 'error');
                return;
            }
            
            try {
                const value = globalSettingsPersistence.get(key);
                log(`获取 ${key} = ${JSON.stringify(value)}`, 'success');
            } catch (error) {
                log(`获取失败: ${error.message}`, 'error');
            }
        };
        
        window.exportSettings = function() {
            try {
                const data = globalSettingsPersistence.export();
                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'settings-export.json';
                a.click();
                URL.revokeObjectURL(url);
                
                log('设置已导出', 'success');
            } catch (error) {
                log(`导出失败: ${error.message}`, 'error');
            }
        };
        
        window.importSettings = function() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            
            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (!file) return;
                
                try {
                    const text = await file.text();
                    const data = JSON.parse(text);
                    await globalSettingsPersistence.import(data);
                    
                    log('设置已导入', 'success');
                } catch (error) {
                    log(`导入失败: ${error.message}`, 'error');
                }
            };
            
            input.click();
        };
        
        window.resetSettings = async function() {
            try {
                await globalSettingsPersistence.reset();
                log('设置已重置', 'success');
            } catch (error) {
                log(`重置失败: ${error.message}`, 'error');
            }
        };
        
        window.clearAllData = function() {
            if (confirm('确定要清空所有数据吗？此操作不可撤销。')) {
                localStorage.clear();
                sessionStorage.clear();
                
                // 重新初始化
                globalSettingsPersistence.init();
                
                log('所有数据已清空', 'success');
                refreshSettings();
                refreshStorage();
                updateStats();
            }
        };
        
        window.refreshSettings = refreshSettings;
        window.refreshStorage = refreshStorage;
        
        window.clearLog = function() {
            document.getElementById('logOutput').textContent = '';
        };
        
        log('设置数据持久化测试页面已加载');
    </script>
</body>
</html>
