<!DOCTYPE html>
<html lang="zh-CN" data-theme="auto" data-font-size="md">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息系统测试</title>
    
    <!-- 导入样式文件 -->
    <link rel="stylesheet" href="./styles/variables.css">
    <link rel="stylesheet" href="./styles/components.css">
    <link rel="stylesheet" href="./styles/layout.css">
    <link rel="stylesheet" href="./styles/themes.css">
    
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .test-section h2 {
            margin-top: 0;
            margin-bottom: var(--spacing-md);
            color: var(--color-foreground);
        }
        
        .control-panel {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            flex-wrap: wrap;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .stat-card {
            background: var(--color-surface-hover);
            padding: var(--spacing-md);
            border-radius: var(--radius-sm);
            text-align: center;
        }
        
        .stat-value {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-bold);
            color: var(--color-primary);
        }
        
        .stat-label {
            font-size: var(--font-size-sm);
            color: var(--color-foreground-muted);
            margin-top: var(--spacing-xs);
        }
        
        .search-container {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }
        
        .search-results {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            max-height: 200px;
            overflow-y: auto;
            padding: var(--spacing-sm);
        }
        
        .search-result-item {
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--color-border);
            cursor: pointer;
        }
        
        .search-result-item:hover {
            background: var(--color-surface-hover);
        }
        
        .search-result-item:last-child {
            border-bottom: none;
        }
        
        .log-output {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            font-family: monospace;
            font-size: var(--font-size-sm);
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .message-preview {
            background: var(--color-surface-hover);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-sm);
            margin: var(--spacing-xs) 0;
            font-size: var(--font-size-sm);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>消息系统测试</h1>
        
        <!-- 控制面板 -->
        <div class="test-section">
            <h2>控制面板</h2>
            <div class="control-panel">
                <button class="btn btn-primary" onclick="addTestMessage()">添加测试消息</button>
                <button class="btn btn-secondary" onclick="addUserMessage()">添加用户消息</button>
                <button class="btn btn-secondary" onclick="addAIMessage()">添加AI消息</button>
                <button class="btn btn-secondary" onclick="addLongMessage()">添加长消息</button>
                <button class="btn btn-secondary" onclick="clearAllMessages()">清空消息</button>
                <button class="btn btn-ghost" onclick="refreshStats()">刷新统计</button>
            </div>
        </div>
        
        <!-- 统计信息 -->
        <div class="test-section">
            <h2>消息统计</h2>
            <div class="stats-grid" id="statsGrid">
                <!-- 统计卡片将在这里动态生成 -->
            </div>
        </div>
        
        <!-- 搜索功能 -->
        <div class="test-section">
            <h2>消息搜索</h2>
            <div class="search-container">
                <input type="text" id="searchInput" class="input" placeholder="输入搜索关键词..." style="flex: 1;">
                <button class="btn btn-primary" onclick="searchMessages()">搜索</button>
                <button class="btn btn-secondary" onclick="clearSearch()">清除</button>
            </div>
            <div id="searchResults" class="search-results hidden"></div>
        </div>
        
        <!-- 批量操作 -->
        <div class="test-section">
            <h2>批量操作</h2>
            <div class="control-panel">
                <button class="btn btn-secondary" onclick="selectAllMessages()">全选消息</button>
                <button class="btn btn-secondary" onclick="selectUserMessages()">选择用户消息</button>
                <button class="btn btn-secondary" onclick="selectAIMessages()">选择AI消息</button>
                <button class="btn btn-secondary" onclick="deleteSelectedMessages()">删除选中</button>
                <button class="btn btn-ghost" onclick="clearSelection()">清除选择</button>
            </div>
            <div id="selectionInfo" class="message-preview"></div>
        </div>
        
        <!-- 导出功能 -->
        <div class="test-section">
            <h2>导出功能</h2>
            <div class="control-panel">
                <button class="btn btn-secondary" onclick="exportAsJSON()">导出为JSON</button>
                <button class="btn btn-secondary" onclick="exportAsText()">导出为文本</button>
                <button class="btn btn-secondary" onclick="exportAsCSV()">导出为CSV</button>
            </div>
        </div>
        
        <!-- 消息显示区域 -->
        <div class="test-section">
            <h2>消息显示</h2>
            <div class="app-container">
                <div class="chat-container">
                    <div class="messages-container" id="messages" style="height: 400px;">
                        <!-- 消息将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 操作日志 -->
        <div class="test-section">
            <h2>操作日志</h2>
            <div class="control-panel">
                <button class="btn btn-ghost" onclick="clearLog()">清空日志</button>
            </div>
            <div id="logOutput" class="log-output"></div>
        </div>
        
        <!-- Toast容器 -->
        <div class="toast-container" id="toastContainer"></div>
    </div>
    
    <script type="module">
        import { Store } from './scripts/state/store.js';
        import { MessageComponent } from './scripts/components/message.js';
        import { ToastComponent } from './scripts/components/toast.js';
        import { EventManager } from './scripts/utils/events.js';
        import { MessageTypes, MessageStatus } from './scripts/components/message-manager.js';
        
        // 初始化组件
        const store = new Store();
        const eventManager = new EventManager();
        const messageComponent = new MessageComponent(store, eventManager);
        const toastComponent = new ToastComponent();
        
        // 全局变量
        window.store = store;
        window.messageComponent = messageComponent;
        window.toastComponent = toastComponent;
        window.selectedMessageIds = new Set();
        
        // 初始化组件
        Promise.all([
            messageComponent.init(),
            toastComponent.init()
        ]).then(() => {
            log('消息系统测试页面初始化完成');
            refreshStats();
        }).catch(error => {
            log(`初始化失败: ${error.message}`, 'error');
        });
        
        // 日志函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        // 显示Toast
        function showToast(message, type = 'info') {
            toastComponent.show(message, type);
        }
        
        // 测试函数
        window.addTestMessage = function() {
            const messages = [
                '这是一条测试消息',
                '你好，我是AI助手',
                '今天天气怎么样？',
                '请帮我写一段代码',
                '谢谢你的帮助！'
            ];
            
            const randomMessage = messages[Math.floor(Math.random() * messages.length)];
            const isUser = Math.random() > 0.5;
            
            messageComponent.addMessage({
                content: randomMessage,
                isUser: isUser,
                type: MessageTypes.TEXT,
                status: MessageStatus.SENT
            });
            
            log(`添加${isUser ? '用户' : 'AI'}消息: ${randomMessage}`);
            refreshStats();
        };
        
        window.addUserMessage = function() {
            const userMessages = [
                '你能帮我解决这个问题吗？',
                '我需要一些建议',
                '这个功能怎么使用？',
                '有什么推荐的吗？'
            ];
            
            const randomMessage = userMessages[Math.floor(Math.random() * userMessages.length)];
            
            messageComponent.addMessage({
                content: randomMessage,
                isUser: true,
                type: MessageTypes.TEXT,
                status: MessageStatus.SENT
            });
            
            log(`添加用户消息: ${randomMessage}`);
            refreshStats();
        };
        
        window.addAIMessage = function() {
            const aiMessages = [
                '我很乐意帮助您解决这个问题。',
                '根据您的需求，我建议...',
                '这个功能的使用方法是...',
                '我推荐以下几个选项：'
            ];
            
            const randomMessage = aiMessages[Math.floor(Math.random() * aiMessages.length)];
            
            messageComponent.addMessage({
                content: randomMessage,
                isUser: false,
                type: MessageTypes.TEXT,
                status: MessageStatus.SENT
            });
            
            log(`添加AI消息: ${randomMessage}`);
            refreshStats();
        };
        
        window.addLongMessage = function() {
            const longMessage = `这是一条很长的测试消息，用来测试消息折叠功能。
            
这条消息包含多行文本，应该会触发自动折叠功能。当消息超过5行时，系统会自动添加展开/收起按钮。

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

这是最后一行文本，用来确保消息足够长以触发折叠功能。`;
            
            messageComponent.addMessage({
                content: longMessage,
                isUser: Math.random() > 0.5,
                type: MessageTypes.TEXT,
                status: MessageStatus.SENT
            });
            
            log('添加长消息（测试折叠功能）');
            refreshStats();
        };
        
        window.clearAllMessages = function() {
            messageComponent.clearMessages();
            selectedMessageIds.clear();
            log('清空所有消息');
            refreshStats();
            updateSelectionInfo();
            showToast('所有消息已清空', 'success');
        };
        
        window.refreshStats = function() {
            const stats = messageComponent.getStatistics();
            const statsGrid = document.getElementById('statsGrid');
            
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${stats.total}</div>
                    <div class="stat-label">总消息数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.userMessages}</div>
                    <div class="stat-label">用户消息</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.aiMessages}</div>
                    <div class="stat-label">AI消息</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.editedMessages}</div>
                    <div class="stat-label">已编辑</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.averageLength}</div>
                    <div class="stat-label">平均长度</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${Object.values(stats.typeStats).reduce((a, b) => a + b, 0)}</div>
                    <div class="stat-label">类型统计</div>
                </div>
            `;
            
            log('统计信息已刷新');
        };
        
        window.searchMessages = function() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) {
                showToast('请输入搜索关键词', 'warning');
                return;
            }
            
            const results = messageComponent.searchMessages(query);
            const resultsContainer = document.getElementById('searchResults');
            
            if (results.length === 0) {
                resultsContainer.innerHTML = '<div class="search-result-item">未找到匹配的消息</div>';
            } else {
                resultsContainer.innerHTML = results.map(message => {
                    const preview = message.content.length > 100 
                        ? message.content.substring(0, 100) + '...'
                        : message.content;
                    const sender = message.isUser ? '用户' : 'AI';
                    const time = new Date(message.timestamp).toLocaleTimeString();
                    
                    return `
                        <div class="search-result-item" onclick="scrollToMessage('${message.id}')">
                            <strong>[${time}] ${sender}:</strong> ${preview}
                        </div>
                    `;
                }).join('');
            }
            
            resultsContainer.classList.remove('hidden');
            messageComponent.highlightSearchResults(query);
            
            log(`搜索 "${query}" 找到 ${results.length} 条结果`);
        };
        
        window.clearSearch = function() {
            document.getElementById('searchInput').value = '';
            document.getElementById('searchResults').classList.add('hidden');
            messageComponent.clearHighlights();
            log('清除搜索结果');
        };
        
        window.scrollToMessage = function(messageId) {
            const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
            if (messageElement) {
                messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                messageElement.style.backgroundColor = 'var(--color-primary)';
                messageElement.style.opacity = '0.3';
                setTimeout(() => {
                    messageElement.style.backgroundColor = '';
                    messageElement.style.opacity = '';
                }, 1000);
            }
        };
        
        window.selectAllMessages = function() {
            const messages = store.getState('messages') || [];
            selectedMessageIds.clear();
            messages.forEach(message => selectedMessageIds.add(message.id));
            updateSelectionInfo();
            log(`选择了 ${selectedMessageIds.size} 条消息`);
        };
        
        window.selectUserMessages = function() {
            const messages = messageComponent.messageManager.getUserMessages();
            selectedMessageIds.clear();
            messages.forEach(message => selectedMessageIds.add(message.id));
            updateSelectionInfo();
            log(`选择了 ${selectedMessageIds.size} 条用户消息`);
        };
        
        window.selectAIMessages = function() {
            const messages = messageComponent.messageManager.getAIMessages();
            selectedMessageIds.clear();
            messages.forEach(message => selectedMessageIds.add(message.id));
            updateSelectionInfo();
            log(`选择了 ${selectedMessageIds.size} 条AI消息`);
        };
        
        window.deleteSelectedMessages = function() {
            if (selectedMessageIds.size === 0) {
                showToast('请先选择要删除的消息', 'warning');
                return;
            }
            
            const count = messageComponent.batchDeleteMessages(Array.from(selectedMessageIds));
            selectedMessageIds.clear();
            updateSelectionInfo();
            refreshStats();
            
            log(`批量删除了 ${count} 条消息`);
            showToast(`已删除 ${count} 条消息`, 'success');
        };
        
        window.clearSelection = function() {
            selectedMessageIds.clear();
            updateSelectionInfo();
            log('清除选择');
        };
        
        function updateSelectionInfo() {
            const info = document.getElementById('selectionInfo');
            if (selectedMessageIds.size === 0) {
                info.textContent = '未选择任何消息';
            } else {
                info.textContent = `已选择 ${selectedMessageIds.size} 条消息`;
            }
        }
        
        window.exportAsJSON = function() {
            try {
                const data = messageComponent.exportMessagesAdvanced('json');
                downloadFile('messages.json', data, 'application/json');
                log('导出JSON格式成功');
                showToast('JSON文件已导出', 'success');
            } catch (error) {
                log(`导出JSON失败: ${error.message}`, 'error');
                showToast('导出失败', 'error');
            }
        };
        
        window.exportAsText = function() {
            try {
                const data = messageComponent.exportMessagesAdvanced('txt');
                downloadFile('messages.txt', data, 'text/plain');
                log('导出文本格式成功');
                showToast('文本文件已导出', 'success');
            } catch (error) {
                log(`导出文本失败: ${error.message}`, 'error');
                showToast('导出失败', 'error');
            }
        };
        
        window.exportAsCSV = function() {
            try {
                const data = messageComponent.exportMessagesAdvanced('csv');
                downloadFile('messages.csv', data, 'text/csv');
                log('导出CSV格式成功');
                showToast('CSV文件已导出', 'success');
            } catch (error) {
                log(`导出CSV失败: ${error.message}`, 'error');
                showToast('导出失败', 'error');
            }
        };
        
        function downloadFile(filename, content, mimeType) {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        window.clearLog = function() {
            document.getElementById('logOutput').textContent = '';
        };
        
        // 初始化选择信息
        updateSelectionInfo();
    </script>
</body>
</html>
