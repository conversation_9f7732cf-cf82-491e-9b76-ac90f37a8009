/**
 * 存储工具类
 * 提供本地存储、会话存储和内存存储功能
 */

/**
 * 存储工具类
 */
export class StorageUtils {
  static STORAGE_KEYS = {
    SETTINGS: 'chat-settings',
    CHAT_HISTORY: 'chat-history',
    USER_PREFERENCES: 'user-preferences',
    APP_STATE: 'app-state'
  };

  static DEFAULT_SETTINGS = {
    theme: 'auto',
    fontSize: 'md',
    autoScroll: true,
    showTimestamps: true,
    enableNotifications: true,
    maxHistorySize: 1000
  };

  /**
   * 检查存储是否可用
   * @param {Storage} storage - 存储对象
   * @returns {boolean} 是否可用
   */
  static isStorageAvailable(storage) {
    try {
      const test = '__storage_test__';
      storage.setItem(test, test);
      storage.removeItem(test);
      return true;
    } catch (e) {
      return false;
    }
  }

  /**
   * 安全地获取存储项
   * @param {Storage} storage - 存储对象
   * @param {string} key - 键
   * @param {any} defaultValue - 默认值
   * @returns {any} 存储的值或默认值
   */
  static safeGetItem(storage, key, defaultValue = null) {
    try {
      if (!this.isStorageAvailable(storage)) {
        return defaultValue;
      }
      
      const item = storage.getItem(key);
      if (item === null) {
        return defaultValue;
      }
      
      return JSON.parse(item);
    } catch (error) {
      console.error(`Error getting item "${key}" from storage:`, error);
      return defaultValue;
    }
  }

  /**
   * 安全地设置存储项
   * @param {Storage} storage - 存储对象
   * @param {string} key - 键
   * @param {any} value - 值
   * @returns {boolean} 是否成功
   */
  static safeSetItem(storage, key, value) {
    try {
      if (!this.isStorageAvailable(storage)) {
        return false;
      }
      
      storage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error(`Error setting item "${key}" in storage:`, error);
      
      // 如果是存储空间不足，尝试清理
      if (error.name === 'QuotaExceededError') {
        this.cleanup(storage);
        try {
          storage.setItem(key, JSON.stringify(value));
          return true;
        } catch (retryError) {
          console.error('Failed to set item after cleanup:', retryError);
        }
      }
      
      return false;
    }
  }

  /**
   * 安全地移除存储项
   * @param {Storage} storage - 存储对象
   * @param {string} key - 键
   * @returns {boolean} 是否成功
   */
  static safeRemoveItem(storage, key) {
    try {
      if (!this.isStorageAvailable(storage)) {
        return false;
      }
      
      storage.removeItem(key);
      return true;
    } catch (error) {
      console.error(`Error removing item "${key}" from storage:`, error);
      return false;
    }
  }

  /**
   * 获取设置
   * @returns {object} 设置对象
   */
  static getSettings() {
    const settings = this.safeGetItem(localStorage, this.STORAGE_KEYS.SETTINGS, {});
    return { ...this.DEFAULT_SETTINGS, ...settings };
  }

  /**
   * 保存设置
   * @param {object} settings - 设置对象
   * @returns {boolean} 是否成功
   */
  static saveSettings(settings) {
    const currentSettings = this.getSettings();
    const newSettings = { ...currentSettings, ...settings };
    return this.safeSetItem(localStorage, this.STORAGE_KEYS.SETTINGS, newSettings);
  }

  /**
   * 获取聊天历史
   * @returns {Array} 聊天历史数组
   */
  static getChatHistory() {
    return this.safeGetItem(localStorage, this.STORAGE_KEYS.CHAT_HISTORY, []);
  }

  /**
   * 保存聊天历史
   * @param {Array} messages - 消息数组
   * @returns {boolean} 是否成功
   */
  static saveChatHistory(messages) {
    const settings = this.getSettings();
    const maxSize = settings.maxHistorySize || 1000;
    
    // 限制历史记录大小
    const limitedMessages = messages.slice(-maxSize);
    
    return this.safeSetItem(localStorage, this.STORAGE_KEYS.CHAT_HISTORY, limitedMessages);
  }

  /**
   * 添加消息到历史
   * @param {object} message - 消息对象
   * @returns {boolean} 是否成功
   */
  static addMessageToHistory(message) {
    const history = this.getChatHistory();
    history.push({
      ...message,
      id: message.id || this.generateId(),
      timestamp: message.timestamp || Date.now()
    });
    
    return this.saveChatHistory(history);
  }

  /**
   * 清空聊天历史
   * @returns {boolean} 是否成功
   */
  static clearChatHistory() {
    return this.safeRemoveItem(localStorage, this.STORAGE_KEYS.CHAT_HISTORY);
  }

  /**
   * 获取用户偏好
   * @returns {object} 用户偏好对象
   */
  static getUserPreferences() {
    return this.safeGetItem(localStorage, this.STORAGE_KEYS.USER_PREFERENCES, {});
  }

  /**
   * 保存用户偏好
   * @param {object} preferences - 偏好对象
   * @returns {boolean} 是否成功
   */
  static saveUserPreferences(preferences) {
    const currentPreferences = this.getUserPreferences();
    const newPreferences = { ...currentPreferences, ...preferences };
    return this.safeSetItem(localStorage, this.STORAGE_KEYS.USER_PREFERENCES, newPreferences);
  }

  /**
   * 获取应用状态
   * @returns {object} 应用状态对象
   */
  static getAppState() {
    return this.safeGetItem(sessionStorage, this.STORAGE_KEYS.APP_STATE, {});
  }

  /**
   * 保存应用状态
   * @param {object} state - 状态对象
   * @returns {boolean} 是否成功
   */
  static saveAppState(state) {
    return this.safeSetItem(sessionStorage, this.STORAGE_KEYS.APP_STATE, state);
  }

  /**
   * 导出所有数据
   * @returns {object} 导出的数据
   */
  static exportData() {
    return {
      settings: this.getSettings(),
      chatHistory: this.getChatHistory(),
      userPreferences: this.getUserPreferences(),
      exportTime: new Date().toISOString(),
      version: '1.0.0'
    };
  }

  /**
   * 导入数据
   * @param {object} data - 要导入的数据
   * @returns {boolean} 是否成功
   */
  static importData(data) {
    try {
      if (data.settings) {
        this.saveSettings(data.settings);
      }
      
      if (data.chatHistory) {
        this.saveChatHistory(data.chatHistory);
      }
      
      if (data.userPreferences) {
        this.saveUserPreferences(data.userPreferences);
      }
      
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }

  /**
   * 清理存储空间
   * @param {Storage} storage - 存储对象
   */
  static cleanup(storage) {
    try {
      // 获取所有键
      const keys = Object.keys(storage);
      
      // 按时间戳排序，删除最旧的项目
      const itemsWithTime = keys.map(key => {
        try {
          const item = JSON.parse(storage.getItem(key));
          return {
            key,
            timestamp: item.timestamp || 0
          };
        } catch {
          return { key, timestamp: 0 };
        }
      });
      
      itemsWithTime.sort((a, b) => a.timestamp - b.timestamp);
      
      // 删除最旧的25%项目
      const deleteCount = Math.floor(itemsWithTime.length * 0.25);
      for (let i = 0; i < deleteCount; i++) {
        storage.removeItem(itemsWithTime[i].key);
      }
      
      console.log(`Cleaned up ${deleteCount} items from storage`);
    } catch (error) {
      console.error('Error during storage cleanup:', error);
    }
  }

  /**
   * 获取存储使用情况
   * @param {Storage} storage - 存储对象
   * @returns {object} 使用情况信息
   */
  static getStorageUsage(storage) {
    try {
      let totalSize = 0;
      const items = {};
      
      for (let key in storage) {
        if (storage.hasOwnProperty(key)) {
          const size = storage[key].length;
          totalSize += size;
          items[key] = size;
        }
      }
      
      return {
        totalSize,
        items,
        itemCount: Object.keys(items).length
      };
    } catch (error) {
      console.error('Error getting storage usage:', error);
      return { totalSize: 0, items: {}, itemCount: 0 };
    }
  }

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  static generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 重置所有数据
   * @returns {boolean} 是否成功
   */
  static resetAll() {
    try {
      Object.values(this.STORAGE_KEYS).forEach(key => {
        this.safeRemoveItem(localStorage, key);
        this.safeRemoveItem(sessionStorage, key);
      });
      return true;
    } catch (error) {
      console.error('Error resetting storage:', error);
      return false;
    }
  }
}

/**
 * 内存存储类（用于不支持本地存储的环境）
 */
export class MemoryStorage {
  constructor() {
    this.data = new Map();
  }

  getItem(key) {
    return this.data.get(key) || null;
  }

  setItem(key, value) {
    this.data.set(key, value);
  }

  removeItem(key) {
    this.data.delete(key);
  }

  clear() {
    this.data.clear();
  }

  get length() {
    return this.data.size;
  }

  key(index) {
    const keys = Array.from(this.data.keys());
    return keys[index] || null;
  }
}

// 导出默认实例
export default StorageUtils;
