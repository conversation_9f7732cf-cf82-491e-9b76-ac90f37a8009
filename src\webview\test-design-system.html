<!DOCTYPE html>
<html lang="zh-CN" data-theme="auto" data-font-size="md">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS设计系统测试</title>
    
    <!-- 导入样式文件 -->
    <link rel="stylesheet" href="./styles/variables.css">
    <link rel="stylesheet" href="./styles/components.css">
    <link rel="stylesheet" href="./styles/layout.css">
    <link rel="stylesheet" href="./styles/themes.css">
</head>
<body>
    <div class="app-container">
        <div class="container">
            <h1>CSS设计系统测试页面</h1>
            
            <!-- 主题切换测试 -->
            <section class="settings-section">
                <h2 class="settings-section-title">主题切换测试</h2>
                <div class="flex gap-md">
                    <button class="btn btn-primary" onclick="setTheme('auto')">自动</button>
                    <button class="btn btn-secondary" onclick="setTheme('light')">浅色</button>
                    <button class="btn btn-secondary" onclick="setTheme('dark')">深色</button>
                    <button class="btn btn-secondary" onclick="setTheme('high-contrast')">高对比度</button>
                </div>
            </section>
            
            <!-- 字号测试 -->
            <section class="settings-section">
                <h2 class="settings-section-title">字号测试</h2>
                <div class="flex gap-md">
                    <button class="btn btn-ghost" onclick="setFontSize('xs')">XS</button>
                    <button class="btn btn-ghost" onclick="setFontSize('sm')">SM</button>
                    <button class="btn btn-ghost" onclick="setFontSize('md')">MD</button>
                    <button class="btn btn-ghost" onclick="setFontSize('lg')">LG</button>
                    <button class="btn btn-ghost" onclick="setFontSize('xl')">XL</button>
                    <button class="btn btn-ghost" onclick="setFontSize('2xl')">2XL</button>
                </div>
            </section>
            
            <!-- 按钮组件测试 -->
            <section class="settings-section">
                <h2 class="settings-section-title">按钮组件</h2>
                <div class="flex gap-md flex-wrap">
                    <button class="btn btn-primary">主要按钮</button>
                    <button class="btn btn-secondary">次要按钮</button>
                    <button class="btn btn-ghost">幽灵按钮</button>
                    <button class="btn btn-primary" disabled>禁用按钮</button>
                    <button class="btn btn-icon">⚙️</button>
                </div>
            </section>
            
            <!-- 输入框测试 -->
            <section class="settings-section">
                <h2 class="settings-section-title">输入框组件</h2>
                <div class="flex flex-col gap-md">
                    <input class="input" type="text" placeholder="普通输入框">
                    <textarea class="input" rows="3" placeholder="多行文本框"></textarea>
                </div>
            </section>
            
            <!-- 消息组件测试 -->
            <section class="settings-section">
                <h2 class="settings-section-title">消息组件</h2>
                <div class="flex flex-col gap-md">
                    <div class="message message-user">
                        <div class="message-content">
                            这是一条用户消息，用来测试消息组件的样式。
                            <div class="message-actions">
                                <button class="btn btn-icon">✏️</button>
                                <button class="btn btn-icon">🗑️</button>
                            </div>
                        </div>
                        <div class="message-time">14:30</div>
                    </div>
                    
                    <div class="message message-ai">
                        <div class="message-content">
                            这是一条AI回复消息，展示了AI消息的样式效果。这条消息比较长，用来测试长文本的显示效果和折叠功能。
                        </div>
                        <div class="message-time">14:31</div>
                    </div>
                    
                    <div class="message message-ai">
                        <div class="message-content message-collapsed">
                            这是一条很长的消息，用来测试消息折叠功能。这条消息包含了很多文本内容，应该会被自动折叠。当消息超过5行时，应该显示展开按钮。这里继续添加更多文本来测试折叠效果。Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                        </div>
                        <button class="message-expand-btn">展开</button>
                        <div class="message-time">14:32</div>
                    </div>
                </div>
            </section>
            
            <!-- Toast通知测试 -->
            <section class="settings-section">
                <h2 class="settings-section-title">Toast通知</h2>
                <div class="flex gap-md">
                    <button class="btn btn-secondary" onclick="showToast('success')">成功</button>
                    <button class="btn btn-secondary" onclick="showToast('warning')">警告</button>
                    <button class="btn btn-secondary" onclick="showToast('error')">错误</button>
                    <button class="btn btn-secondary" onclick="showToast('info')">信息</button>
                </div>
            </section>
            
            <!-- 网格系统测试 -->
            <section class="settings-section">
                <h2 class="settings-section-title">响应式网格系统</h2>
                <div class="grid">
                    <div class="col-12 md:col-6 lg:col-4" style="background: var(--color-surface-hover); padding: var(--spacing-md); border-radius: var(--radius-md);">
                        列 1
                    </div>
                    <div class="col-12 md:col-6 lg:col-4" style="background: var(--color-surface-hover); padding: var(--spacing-md); border-radius: var(--radius-md);">
                        列 2
                    </div>
                    <div class="col-12 md:col-12 lg:col-4" style="background: var(--color-surface-hover); padding: var(--spacing-md); border-radius: var(--radius-md);">
                        列 3
                    </div>
                </div>
            </section>
        </div>
        
        <!-- Toast容器 -->
        <div class="toast-container" id="toastContainer"></div>
    </div>
    
    <script type="module">
        import { globalThemeManager } from './scripts/utils/theme.js';
        
        // 初始化主题管理器
        globalThemeManager.init();
        
        // 全局函数
        window.setTheme = (theme) => {
            globalThemeManager.setTheme(theme);
        };
        
        window.setFontSize = (size) => {
            document.documentElement.setAttribute('data-font-size', size);
        };
        
        window.showToast = (type) => {
            const container = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast toast-${type} show`;
            
            toast.innerHTML = `
                <div class="toast-header">
                    <span class="toast-title">${type.charAt(0).toUpperCase() + type.slice(1)}</span>
                    <button class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
                <div class="toast-body">
                    这是一条${type}类型的通知消息。
                </div>
            `;
            
            container.appendChild(toast);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.classList.remove('show');
                    setTimeout(() => toast.remove(), 300);
                }
            }, 3000);
        };
        
        // 监听主题变化
        document.addEventListener('themechange', (e) => {
            console.log('主题已切换:', e.detail);
        });
        
        console.log('CSS设计系统测试页面已加载');
    </script>
</body>
</html>
