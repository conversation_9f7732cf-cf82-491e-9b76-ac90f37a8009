/**
 * 通知管理器
 * 提供高级的通知管理和队列处理功能
 */

import BaseComponent from './base.js';
import { DOMUtils } from '../utils/dom.js';
import { EventManager } from '../utils/events.js';

/**
 * 通知类型枚举
 */
export const NotificationTypes = {
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error',
  INFO: 'info',
  LOADING: 'loading'
};

/**
 * 通知位置枚举
 */
export const NotificationPositions = {
  TOP_RIGHT: 'top-right',
  TOP_LEFT: 'top-left',
  TOP_CENTER: 'top-center',
  BOTTOM_RIGHT: 'bottom-right',
  BOTTOM_LEFT: 'bottom-left',
  BOTTOM_CENTER: 'bottom-center'
};

/**
 * 通知管理器类
 */
export class NotificationManager extends BaseComponent {
  constructor(container, options = {}) {
    super(container, {
      className: 'notification-manager',
      autoInit: true,
      ...options
    });
    
    this.eventManager = new EventManager();
    this.notifications = new Map();
    this.queue = [];
    this.isProcessingQueue = false;
    
    // 配置选项
    this.config = {
      maxNotifications: 5,
      defaultDuration: 4000,
      position: NotificationPositions.TOP_RIGHT,
      enableQueue: true,
      enableGrouping: true,
      enableSound: false,
      animationDuration: 300,
      stackSpacing: 10,
      ...options.config
    };
    
    // 通知计数器
    this.notificationCounter = 0;
    
    // 绑定方法上下文
    this.show = this.show.bind(this);
    this.hide = this.hide.bind(this);
    this.clear = this.clear.bind(this);
    this.processQueue = this.processQueue.bind(this);
  }

  /**
   * 获取初始状态
   */
  getInitialState() {
    return {
      ...super.getInitialState(),
      activeNotifications: 0,
      queuedNotifications: 0,
      totalNotifications: 0
    };
  }

  /**
   * 初始化后钩子
   */
  async afterInit() {
    // 设置容器位置
    this.setupContainer();
    
    // 监听全局通知事件
    this.setupGlobalListeners();
  }

  /**
   * 设置容器
   */
  setupContainer() {
    if (!this.element) return;
    
    this.element.className = `notification-manager position-${this.config.position}`;
    
    // 设置容器样式
    const positions = {
      'top-right': { top: '20px', right: '20px' },
      'top-left': { top: '20px', left: '20px' },
      'top-center': { top: '20px', left: '50%', transform: 'translateX(-50%)' },
      'bottom-right': { bottom: '20px', right: '20px' },
      'bottom-left': { bottom: '20px', left: '20px' },
      'bottom-center': { bottom: '20px', left: '50%', transform: 'translateX(-50%)' }
    };
    
    const position = positions[this.config.position];
    Object.assign(this.element.style, {
      position: 'fixed',
      zIndex: '10000',
      pointerEvents: 'none',
      ...position
    });
  }

  /**
   * 设置全局监听器
   */
  setupGlobalListeners() {
    // 监听全局通知事件
    window.addEventListener('notification:show', (e) => {
      const { message, type, options } = e.detail;
      this.show(message, type, options);
    });
    
    window.addEventListener('notification:hide', (e) => {
      const { id } = e.detail;
      this.hide(id);
    });
    
    window.addEventListener('notification:clear', () => {
      this.clear();
    });
  }

  /**
   * 显示通知
   * @param {string} message - 消息内容
   * @param {string} type - 通知类型
   * @param {object} options - 选项
   * @returns {string} 通知ID
   */
  show(message, type = NotificationTypes.INFO, options = {}) {
    const notificationOptions = {
      duration: this.config.defaultDuration,
      closable: true,
      persistent: false,
      actions: [],
      groupKey: null,
      priority: 0,
      ...options
    };
    
    // 生成通知ID
    const id = `notification-${++this.notificationCounter}`;
    
    // 创建通知对象
    const notification = {
      id,
      message,
      type,
      options: notificationOptions,
      timestamp: Date.now(),
      element: null,
      timer: null
    };
    
    // 检查是否需要分组
    if (this.config.enableGrouping && notificationOptions.groupKey) {
      const existingNotification = this.findNotificationByGroup(notificationOptions.groupKey);
      if (existingNotification) {
        this.updateGroupedNotification(existingNotification, notification);
        return existingNotification.id;
      }
    }
    
    // 检查队列
    if (this.config.enableQueue && this.notifications.size >= this.config.maxNotifications) {
      this.queue.push(notification);
      this.setState('queuedNotifications', this.queue.length);
      this.processQueue();
      return id;
    }
    
    // 立即显示通知
    this.displayNotification(notification);
    
    return id;
  }

  /**
   * 查找分组通知
   * @param {string} groupKey - 分组键
   * @returns {object|null} 通知对象
   */
  findNotificationByGroup(groupKey) {
    for (const notification of this.notifications.values()) {
      if (notification.options.groupKey === groupKey) {
        return notification;
      }
    }
    return null;
  }

  /**
   * 更新分组通知
   * @param {object} existingNotification - 现有通知
   * @param {object} newNotification - 新通知
   */
  updateGroupedNotification(existingNotification, newNotification) {
    // 更新消息内容
    existingNotification.message = newNotification.message;
    existingNotification.timestamp = newNotification.timestamp;
    
    // 更新显示
    const messageElement = existingNotification.element?.querySelector('.notification-message');
    if (messageElement) {
      messageElement.textContent = newNotification.message;
    }
    
    // 重置定时器
    if (existingNotification.timer) {
      clearTimeout(existingNotification.timer);
    }
    
    if (!newNotification.options.persistent) {
      existingNotification.timer = setTimeout(() => {
        this.hide(existingNotification.id);
      }, newNotification.options.duration);
    }
    
    // 添加更新动画
    if (existingNotification.element) {
      existingNotification.element.classList.add('notification-updated');
      setTimeout(() => {
        existingNotification.element?.classList.remove('notification-updated');
      }, this.config.animationDuration);
    }
  }

  /**
   * 显示通知
   * @param {object} notification - 通知对象
   */
  displayNotification(notification) {
    // 创建通知元素
    const element = this.createNotificationElement(notification);
    notification.element = element;
    
    // 添加到容器
    this.element.appendChild(element);
    
    // 添加到管理器
    this.notifications.set(notification.id, notification);
    
    // 更新状态
    this.setState('activeNotifications', this.notifications.size);
    this.setState('totalNotifications', this.state.totalNotifications + 1);
    
    // 添加进入动画
    requestAnimationFrame(() => {
      element.classList.add('notification-enter');
    });
    
    // 设置自动隐藏
    if (!notification.options.persistent) {
      notification.timer = setTimeout(() => {
        this.hide(notification.id);
      }, notification.options.duration);
    }
    
    // 触发事件
    this.eventManager.emit('notificationShown', notification);
    
    // 播放声音（如果启用）
    if (this.config.enableSound) {
      this.playNotificationSound(notification.type);
    }
  }

  /**
   * 创建通知元素
   * @param {object} notification - 通知对象
   * @returns {Element} 通知元素
   */
  createNotificationElement(notification) {
    const { message, type, options } = notification;
    
    const element = DOMUtils.createElement('div', {
      className: `notification notification-${type}`,
      style: {
        pointerEvents: 'auto',
        marginBottom: `${this.config.stackSpacing}px`
      }
    });
    
    // 通知图标
    const icon = this.getNotificationIcon(type);
    const iconElement = DOMUtils.createElement('div', {
      className: 'notification-icon',
      innerHTML: icon
    });
    
    // 通知内容
    const contentElement = DOMUtils.createElement('div', {
      className: 'notification-content'
    });
    
    const messageElement = DOMUtils.createElement('div', {
      className: 'notification-message',
      textContent: message
    });
    
    contentElement.appendChild(messageElement);
    
    // 操作按钮
    if (options.actions && options.actions.length > 0) {
      const actionsElement = DOMUtils.createElement('div', {
        className: 'notification-actions'
      });
      
      options.actions.forEach(action => {
        const button = DOMUtils.createElement('button', {
          className: `btn btn-sm notification-action-btn`,
          textContent: action.label
        });
        
        button.addEventListener('click', () => {
          if (action.handler) {
            action.handler(notification);
          }
          if (action.closeOnClick !== false) {
            this.hide(notification.id);
          }
        });
        
        actionsElement.appendChild(button);
      });
      
      contentElement.appendChild(actionsElement);
    }
    
    // 关闭按钮
    if (options.closable) {
      const closeButton = DOMUtils.createElement('button', {
        className: 'notification-close-btn',
        innerHTML: '×',
        title: '关闭'
      });
      
      closeButton.addEventListener('click', () => {
        this.hide(notification.id);
      });
      
      element.appendChild(closeButton);
    }
    
    // 进度条（用于显示剩余时间）
    if (!options.persistent && options.showProgress !== false) {
      const progressElement = DOMUtils.createElement('div', {
        className: 'notification-progress'
      });
      
      const progressBar = DOMUtils.createElement('div', {
        className: 'notification-progress-bar'
      });
      
      progressElement.appendChild(progressBar);
      element.appendChild(progressElement);
      
      // 启动进度动画
      requestAnimationFrame(() => {
        progressBar.style.transition = `width ${options.duration}ms linear`;
        progressBar.style.width = '0%';
      });
    }
    
    element.appendChild(iconElement);
    element.appendChild(contentElement);
    
    return element;
  }

  /**
   * 获取通知图标
   * @param {string} type - 通知类型
   * @returns {string} 图标HTML
   */
  getNotificationIcon(type) {
    const icons = {
      [NotificationTypes.SUCCESS]: '✓',
      [NotificationTypes.WARNING]: '⚠',
      [NotificationTypes.ERROR]: '✕',
      [NotificationTypes.INFO]: 'ℹ',
      [NotificationTypes.LOADING]: '⟳'
    };
    
    return icons[type] || icons[NotificationTypes.INFO];
  }

  /**
   * 隐藏通知
   * @param {string} id - 通知ID
   */
  hide(id) {
    const notification = this.notifications.get(id);
    if (!notification) return;
    
    // 清除定时器
    if (notification.timer) {
      clearTimeout(notification.timer);
    }
    
    // 添加退出动画
    if (notification.element) {
      notification.element.classList.add('notification-exit');
      
      setTimeout(() => {
        if (notification.element && notification.element.parentNode) {
          notification.element.parentNode.removeChild(notification.element);
        }
        
        // 从管理器中移除
        this.notifications.delete(id);
        
        // 更新状态
        this.setState('activeNotifications', this.notifications.size);
        
        // 处理队列
        this.processQueue();
        
        // 触发事件
        this.eventManager.emit('notificationHidden', notification);
        
      }, this.config.animationDuration);
    }
  }

  /**
   * 清空所有通知
   */
  clear() {
    const notificationIds = Array.from(this.notifications.keys());
    notificationIds.forEach(id => this.hide(id));
    
    // 清空队列
    this.queue = [];
    this.setState('queuedNotifications', 0);
    
    this.eventManager.emit('notificationsCleared');
  }

  /**
   * 处理队列
   */
  processQueue() {
    if (this.isProcessingQueue || this.queue.length === 0) return;
    if (this.notifications.size >= this.config.maxNotifications) return;
    
    this.isProcessingQueue = true;
    
    // 按优先级排序
    this.queue.sort((a, b) => (b.options.priority || 0) - (a.options.priority || 0));
    
    // 显示下一个通知
    const notification = this.queue.shift();
    if (notification) {
      this.displayNotification(notification);
      this.setState('queuedNotifications', this.queue.length);
    }
    
    this.isProcessingQueue = false;
    
    // 继续处理队列
    if (this.queue.length > 0 && this.notifications.size < this.config.maxNotifications) {
      setTimeout(() => this.processQueue(), 100);
    }
  }

  /**
   * 播放通知声音
   * @param {string} type - 通知类型
   */
  playNotificationSound(type) {
    // 这里可以根据类型播放不同的声音
    // 由于浏览器限制，需要用户交互后才能播放声音
    try {
      const audio = new Audio();
      audio.volume = 0.3;
      
      // 根据类型设置不同的音调
      const frequencies = {
        [NotificationTypes.SUCCESS]: 800,
        [NotificationTypes.WARNING]: 600,
        [NotificationTypes.ERROR]: 400,
        [NotificationTypes.INFO]: 500
      };
      
      // 这里可以使用Web Audio API生成简单的提示音
      // 或者加载预设的音频文件
      
    } catch (error) {
      // 忽略音频播放错误
    }
  }

  /**
   * 获取统计信息
   * @returns {object} 统计信息
   */
  getStatistics() {
    return {
      active: this.notifications.size,
      queued: this.queue.length,
      total: this.state.totalNotifications,
      maxNotifications: this.config.maxNotifications
    };
  }

  /**
   * 更新配置
   * @param {object} newConfig - 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    
    // 重新设置容器
    this.setupContainer();
    
    this.eventManager.emit('configUpdated', this.config);
  }

  /**
   * 获取事件管理器
   * @returns {EventManager} 事件管理器
   */
  getEventManager() {
    return this.eventManager;
  }

  /**
   * 销毁前钩子
   */
  beforeDestroy() {
    // 清空所有通知
    this.clear();
    
    // 清理事件监听器
    this.eventManager.removeAllListeners();
  }
}

// 创建全局实例
export const globalNotificationManager = new NotificationManager(
  document.getElementById('toastContainer') || document.body
);

export default NotificationManager;
